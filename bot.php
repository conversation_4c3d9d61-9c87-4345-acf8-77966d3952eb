<?php

// Include configuration file
require_once 'config.php';

define('API_KEY', $API_KEY);

// JSON Database Functions
function loadJsonData($filename)
{
    if (!file_exists($filename)) {
        return [];
    }
    $data = file_get_contents($filename);
    return json_decode($data, true) ?: [];
}

function saveJsonData($filename, $data)
{
    file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function saveUserData($user_data)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    $user_id = $user_data['id'];
    $current_time = date('Y-m-d H:i:s');

    // If user exists, update their data
    if (isset($users[$user_id])) {
        $users[$user_id] = array_merge($users[$user_id], $user_data);
        $users[$user_id]['last_activity'] = $current_time;
    } else {
        // New user
        $user_data['join_date'] = $current_time;
        $user_data['last_activity'] = $current_time;
        $users[$user_id] = $user_data;
    }

    saveJsonData($USERS_JSON_FILE, $users);
}

function getUserData($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return isset($users[$user_id]) ? $users[$user_id] : null;
}

function getAllUsers()
{
    global $USERS_JSON_FILE;
    return loadJsonData($USERS_JSON_FILE);
}

// Channel Management Functions
function saveChannelData($channel_data)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    $channel_id = $channel_data['id'];
    $current_time = date('Y-m-d H:i:s');

    $channel_data['created_at'] = $current_time;
    $channel_data['updated_at'] = $current_time;
    $channels[$channel_id] = $channel_data;

    saveJsonData($CHANNELS_JSON_FILE, $channels);
}

function isChannelAlreadyAdded($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]);
}

function getChannelOwner($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]) ? $channels[$channel_id]['owner_id'] : null;
}

function getUserChannels($user_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    $user_channels = [];

    foreach ($channels as $channel) {
        if ($channel['owner_id'] == $user_id) {
            $user_channels[] = $channel;
        }
    }

    return $user_channels;
}

function getChannelById($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]) ? $channels[$channel_id] : null;
}

function updateChannelSignature($channel_id, $new_signature)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    if (isset($channels[$channel_id])) {
        $channels[$channel_id]['signature'] = $new_signature;
        $channels[$channel_id]['updated_at'] = date('Y-m-d H:i:s');
        saveJsonData($CHANNELS_JSON_FILE, $channels);
        return true;
    }

    return false;
}

function deleteChannel($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    if (isset($channels[$channel_id])) {
        unset($channels[$channel_id]);
        saveJsonData($CHANNELS_JSON_FILE, $channels);
        return true;
    }

    return false;
}

function setUserState($user_id, $state, $data = [])
{
    $user_data = getUserData($user_id);
    if ($user_data) {
        $user_data['state'] = $state;
        $user_data['state_data'] = $data;
        saveUserData($user_data);
    }
}

function getUserState($user_id)
{
    $user_data = getUserData($user_id);
    return $user_data ? ($user_data['state'] ?? null) : null;
}

function getUserStateData($user_id)
{
    $user_data = getUserData($user_id);
    return $user_data ? ($user_data['state_data'] ?? []) : [];
}

function clearUserState($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    if (isset($users[$user_id])) {
        // Directly modify the users array to ensure state is cleared
        unset($users[$user_id]['state']);
        unset($users[$user_id]['state_data']);
        $users[$user_id]['last_activity'] = date('Y-m-d H:i:s');

        // Save directly to avoid any issues with saveUserData function
        saveJsonData($USERS_JSON_FILE, $users);

        // Log the clearing action
        error_log("Cleared state for user $user_id");
    }
}

function forceClearUserState($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    if (isset($users[$user_id])) {
        // Force clear by setting to null first, then unsetting
        $users[$user_id]['state'] = null;
        $users[$user_id]['state_data'] = null;
        unset($users[$user_id]['state']);
        unset($users[$user_id]['state_data']);
        $users[$user_id]['last_activity'] = date('Y-m-d H:i:s');

        // Save multiple times to ensure it sticks
        saveJsonData($USERS_JSON_FILE, $users);
        saveJsonData($USERS_JSON_FILE, $users);

        error_log("Force cleared state for user $user_id");
    }
}

function handleSignatureEditing($user_id, $chat_id, $text)
{
    // User is sending new signature
    $new_signature = trim($text);
    $state_data = getUserStateData($user_id);

    if (empty($new_signature)) {
        // Clear state if empty signature is sent - ignore the message
        clearUserState($user_id);
        return;
    }

    // Check if signature is too long (more than 200 characters)
    if (strlen($new_signature) > 200) {
        // Clear state if signature is too long - ignore the message
        clearUserState($user_id);
        return;
    }

    if (updateChannelSignature($state_data['channel_id'], $new_signature)) {
        clearUserState($user_id);

        logUserAction($user_id, 'signature_updated', "Signature updated for channel: {$state_data['channel_id']} to: $new_signature");

        $success_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '📣 چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ],
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id,
            "✅ عالی!\n\n" .
            "امضای چنل «{$state_data['channel_title']}» با موفقیت به‌روزرسانی شد.\n\n" .
            "📝 امضای جدید: $new_signature",
            $success_keyboard
        );
    } else {
        sendmessage($chat_id, "❌ خطا در به‌روزرسانی امضا! لطفا دوباره تلاش کنید.");
    }
}

function checkBotAdminStatus($channel_username)
{
    $bot_info = bot('getMe');
    if (!$bot_info || !isset($bot_info->result)) {
        return false;
    }

    $bot_id = $bot_info->result->id;

    $result = bot('getChatMember', [
        'chat_id' => $channel_username,
        'user_id' => $bot_id
    ]);

    if (!$result || !isset($result->result)) {
        return false;
    }

    return in_array($result->result->status, ['administrator', 'creator']);
}

function getChannelInfo($channel_identifier)
{
    $result = bot('getChat', [
        'chat_id' => $channel_identifier
    ]);

    if (!$result || !isset($result->result)) {
        return false;
    }

    return $result->result;
}

function isValidChannelIdentifier($identifier)
{
    $identifier = trim($identifier);

    // Check if it's a numeric ID (private channel)
    if (is_numeric($identifier) || (substr($identifier, 0, 1) === '-' && is_numeric(substr($identifier, 1)))) {
        return true;
    }

    // Check if it's a username (public channel)
    $username_without_at = ltrim($identifier, '@');
    if (strlen($username_without_at) >= 5 && preg_match('/^[a-zA-Z0-9_]+$/', $username_without_at)) {
        return true;
    }

    return false;
}

function getUsersCount()
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return count($users);
}

function isUserExists($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return isset($users[$user_id]);
}

function logUserAction($user_id, $action, $details = '')
{
    $user_data = getUserData($user_id);
    if ($user_data) {
        if (!isset($user_data['actions_log'])) {
            $user_data['actions_log'] = [];
        }

        $user_data['actions_log'][] = [
            'action' => $action,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // Keep only last 50 actions to prevent file from getting too large
        if (count($user_data['actions_log']) > 50) {
            $user_data['actions_log'] = array_slice($user_data['actions_log'], -50);
        }

        saveUserData($user_data);
    }
}

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createMainMenuKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '📣 چنل های من',
                'callback_data' => 'my_channels'
            ],
            [
                'text' => '➕ افزودن چنل',
                'callback_data' => 'add_channel'
            ]
        ],
        [
            [
                'text' => '📚 راهنما',
                'callback_data' => 'help'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function eqi($str1, $str2)
{
    return strtolower(trim($str1)) === strtolower(trim($str2));
}

function getMainMenuMessage($first_name)
{
    return "سلام $first_name 👋\n\n" .
           "به ربات لینک چسبون خوش آمدید!\n\n" .
           "این ربات به شما کمک می‌کند تا پست های کانال خود را به صورت کاملا خودکار ویرایش کنید.\n\n" .
           "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";
}

$update = json_decode(file_get_contents('php://input'));

// Handle channel posts (for automatic signature)
if (isset($update->channel_post)) {
    $post = $update->channel_post;
    $chat_id = $post->chat->id;
    $chat_username = $post->chat->username ?? null;

    // Check if this channel is registered in our system
    $channel = null;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    foreach ($channels as $ch) {
        if ($ch['id'] == $chat_id ||
            ($chat_username && isset($ch['username']) && $ch['username'] == '@' . $chat_username) ||
            (isset($ch['identifier']) && $ch['identifier'] == $chat_id)) {
            $channel = $ch;
            break;
        }
    }

    if ($channel && isset($channel['signature']) && !empty($channel['signature'])) {
        // Don't process forwarded posts
        if (!isset($post->forward_from) && !isset($post->forward_from_chat)) {
            $text = $post->text ?? $post->caption ?? '';

            // Remove any existing signatures (lines containing @)
            $text = preg_replace('/(\n|^).*@.*(\n|$)/', "\n", $text);
            $text = trim($text);

            // Add the new signature
            $text .= "\n\n" . $channel['signature'];

            // Edit the post based on its type
            if (isset($post->text)) {
                // Text post
                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'text' => $text
                ]);
            } elseif (isset($post->caption)) {
                // Media post with caption
                bot('editMessageCaption', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'caption' => $text
                ]);
            }
        }
    }
    exit;
}

// Handle edited channel posts (for automatic signature)
if (isset($update->edited_channel_post)) {
    $post = $update->edited_channel_post;
    $chat_id = $post->chat->id;
    $chat_username = $post->chat->username ?? null;

    // Check if this channel is registered in our system
    $channel = null;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    foreach ($channels as $ch) {
        if ($ch['id'] == $chat_id ||
            ($chat_username && isset($ch['username']) && $ch['username'] == '@' . $chat_username) ||
            (isset($ch['identifier']) && $ch['identifier'] == $chat_id)) {
            $channel = $ch;
            break;
        }
    }

    if ($channel && isset($channel['signature']) && !empty($channel['signature'])) {
        // Don't process forwarded posts
        if (!isset($post->forward_from) && !isset($post->forward_from_chat)) {
            $text = $post->text ?? $post->caption ?? '';

            // Remove any existing signatures (lines containing @)
            $text = preg_replace('/(\n|^).*@.*(\n|$)/', "\n", $text);
            $text = trim($text);

            // Add the new signature
            $text .= "\n\n" . $channel['signature'];

            // Edit the post based on its type
            if (isset($post->text)) {
                // Text post
                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'text' => $text
                ]);
            } elseif (isset($post->caption)) {
                // Media post with caption
                bot('editMessageCaption', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'caption' => $text
                ]);
            }
        }
    }
    exit;
}

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    // Save user data
    $user_data = [
        'id' => $user_id,
        'first_name' => $callback_query->from->first_name ?? '',
        'last_name' => $callback_query->from->last_name ?? '',
        'username' => $callback_query->from->username ?? '',
        'language_code' => $callback_query->from->language_code ?? '',
        'chat_id' => $chat_id
    ];
    saveUserData($user_data);

    if ($data == 'check_membership') {
        logUserAction($user_id, 'check_membership', 'User clicked check membership button');
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'membership_verified', 'User successfully verified membership');
            $first_name = $callback_query->from->first_name ?? 'کاربر';
            $main_menu_keyboard = createMainMenuKeyboard();
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ]);
        } else {
            logUserAction($user_id, 'membership_failed', 'User failed membership verification');
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ شما هنوز عضو نیستید!\n\n" .
                         "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
        }
    } elseif ($data == 'help') {
        logUserAction($user_id, 'help_clicked', 'User clicked help button');
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif ($data == 'add_channel') {
        logUserAction($user_id, 'add_channel_clicked', 'User clicked add channel button');

        // Set user state to adding channel
        setUserState($user_id, 'adding_channel_step1');

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "📣 افزودن چنل جدید\n\n" .
                     "برای افزودن چنل جدید، لطفا مراحل زیر را دنبال کنید:\n\n" .
                     "1️⃣ ابتدا ربات را در چنل خود ادمین کنید\n" .
                     "2️⃣ سپس یوزرنیم یا آیدی چنل را ارسال کنید\n\n" .
                     "💡 نوع چنل‌های پشتیبانی شده:\n" .
                     "🔸 چنل عمومی: @channel_username\n" .
                     "🔸 چنل خصوصی: -1001234567890\n\n" .
                     "⚠️ توجه: ربات باید دسترسی ادمین داشته باشد تا بتواند پست‌ها را ویرایش کند.\n\n" .
                     "لطفا یوزرنیم یا آیدی چنل خود را ارسال کنید:",
            'reply_markup' => $back_keyboard
        ]);
    } elseif ($data == 'my_channels') {
        logUserAction($user_id, 'my_channels_clicked', 'User clicked my channels button');

        $user_channels = getUserChannels($user_id);

        if (empty($user_channels)) {
            $keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '➕ افزودن چنل',
                            'callback_data' => 'add_channel'
                        ],
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "📣 چنل های من\n\n" .
                         "شما هنوز هیچ چنلی ثبت نکرده‌اید.\n\n" .
                         "برای شروع، روی دکمه «افزودن چنل» کلیک کنید.",
                'reply_markup' => $keyboard
            ]);
        } else {
            $channels_text = "📣 چنل های من\n\n";
            $channels_text .= "لطفا چنل مورد نظر خود را انتخاب کنید:\n\n";
            $keyboard_buttons = [];

            // Show channels as buttons
            foreach ($user_channels as $index => $channel) {
                $keyboard_buttons[] = [
                    [
                        'text' => ($channel['title'] ?? 'نامشخص'),
                        'callback_data' => 'manage_channel_' . $channel['id']
                    ]
                ];
            }

            $keyboard_buttons[] = [
                [
                    'text' => '➕ افزودن چنل جدید',
                    'callback_data' => 'add_channel'
                ],
                [
                    'text' => '🔙 بازگشت به منوی اصلی',
                    'callback_data' => 'back_to_main'
                ]
            ];

            $keyboard = json_encode(['inline_keyboard' => $keyboard_buttons]);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $channels_text,
                'reply_markup' => $keyboard
            ]);
        }
    } elseif ($data == 'back_to_main') {
        clearUserState($user_id);
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif (strpos($data, 'manage_channel_') === 0) {
        $channel_id = str_replace('manage_channel_', '', $data);
        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'channel_management_opened', "Opened management for channel: {$channel['identifier']}");

        $management_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '✏️ تغییر امضا',
                        'callback_data' => 'edit_signature_' . $channel_id
                    ],
                    [
                        'text' => '🗑 حذف چنل',
                        'callback_data' => 'delete_channel_' . $channel_id
                    ]
                ],
                [
                    [
                        'text' => '🔙 بازگشت به چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        $channel_info = "📺 مدیریت چنل\n\n";
        $channel_info .= "📣 نام چنل: " . ($channel['title'] ?? 'نامشخص') . "\n";

        // Show appropriate identifier based on channel type
        if (isset($channel['username']) && !empty($channel['username'])) {
            $channel_info .= "🆔 یوزرنیم: " . $channel['username'] . "\n";
        } else {
            $channel_info .= "🆔 آیدی چنل: " . $channel['identifier'] . "\n";
        }

        $channel_info .= "📝 امضای فعلی: " . $channel['signature'] . "\n";
        $channel_info .= "📅 تاریخ ایجاد: " . $channel['created_at'] . "\n\n";
        $channel_info .= "لطفا عملیات مورد نظر خود را انتخاب کنید:";

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $channel_info,
            'reply_markup' => $management_keyboard
        ]);
    } elseif (strpos($data, 'edit_signature_') === 0) {
        $channel_id = str_replace('edit_signature_', '', $data);
        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'edit_signature_started', "Started editing signature for channel: {$channel['identifier']}");

        setUserState($user_id, 'editing_signature', [
            'channel_id' => $channel_id,
            'channel_title' => $channel['title'],
            'current_signature' => $channel['signature']
        ]);

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "✏️ ویرایش امضا\n\n" .
                     "چنل: {$channel['title']}\n" .
                     "امضای فعلی: {$channel['signature']}\n\n" .
                     "لطفا امضای جدید را ارسال کنید:",
            'reply_markup' => $back_keyboard
        ]);
    } elseif (strpos($data, 'delete_channel_') === 0) {
        $channel_id = str_replace('delete_channel_', '', $data);
        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        $confirm_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '✅ بله، حذف کن',
                        'callback_data' => 'confirm_delete_' . $channel_id
                    ],
                    [
                        'text' => '❌ انصراف',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "🗑 حذف چنل\n\n" .
                     "آیا مطمئن هستید که می‌خواهید چنل زیر را حذف کنید؟\n\n" .
                     "📣 {$channel['title']}\n" .
                     "🆔 {$channel['username']}\n\n" .
                     "⚠️ این عمل قابل بازگشت نیست!",
            'reply_markup' => $confirm_keyboard
        ]);
    } elseif (strpos($data, 'confirm_delete_') === 0) {
        $channel_id = str_replace('confirm_delete_', '', $data);
        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        if (deleteChannel($channel_id)) {
            logUserAction($user_id, 'channel_deleted', "Channel deleted: {$channel['identifier']}");

            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '✅ چنل با موفقیت حذف شد.',
                'show_alert' => true
            ]);

            // Redirect to my channels page
            $user_channels = getUserChannels($user_id);

            if (empty($user_channels)) {
                $keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '➕ افزودن چنل',
                                'callback_data' => 'add_channel'
                            ],
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => "📣 چنل های من\n\n" .
                             "شما هنوز هیچ چنلی ثبت نکرده‌اید.\n\n" .
                             "برای شروع، روی دکمه «افزودن چنل» کلیک کنید.",
                    'reply_markup' => $keyboard
                ]);
            } else {
                // Show updated channels list
                $channels_text = "📣 چنل های من\n\n";
                $channels_text .= "لطفا چنل مورد نظر خود را انتخاب کنید:\n\n";
                $keyboard_buttons = [];

                // Show channels as buttons
                foreach ($user_channels as $index => $ch) {
                    $keyboard_buttons[] = [
                        [
                            'text' => ($ch['title'] ?? 'نامشخص'),
                            'callback_data' => 'manage_channel_' . $ch['id']
                        ]
                    ];
                }

                $keyboard_buttons[] = [
                    [
                        'text' => '➕ افزودن چنل جدید',
                        'callback_data' => 'add_channel'
                    ],
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ];

                $keyboard = json_encode(['inline_keyboard' => $keyboard_buttons]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $channels_text,
                    'reply_markup' => $keyboard
                ]);
            }
        } else {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا در حذف چنل!',
                'show_alert' => true
            ]);
        }
    }
    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    // Save user data
    $user_data = [
        'id' => $user_id,
        'first_name' => $message->from->first_name ?? '',
        'last_name' => $message->from->last_name ?? '',
        'username' => $message->from->username ?? '',
        'language_code' => $message->from->language_code ?? '',
        'chat_id' => $chat_id
    ];
    saveUserData($user_data);

    // First check for commands, then check user state
    if ($text == "/start" || $text == "/help") {
        clearUserState($user_id); // Clear any existing state when user uses commands
        $first_name = $message->from->first_name ?? 'کاربر';

        // Log user action after saving user data
        logUserAction($user_id, 'command_used', "User used command: $text");

        if (checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'bot_access_granted', 'User accessed bot with valid membership');
            $main_menu_keyboard = createMainMenuKeyboard();
            $data = [
                'chat_id' => $chat_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ];
            bot('sendMessage', $data);
        } else {
            logUserAction($user_id, 'bot_access_denied', 'User tried to access bot without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                $join_keyboard
            );
        }
        return; // Exit after handling commands
    }

    // Check if user is in a specific state (adding channel process)
    $user_state = getUserState($user_id);

    // Debug: Log the current user state
    if ($user_state) {
        error_log("User $user_id has state: $user_state");
    }

    if ($user_state == 'adding_channel_step1') {
        // User is sending channel identifier (username or ID)
        $channel_identifier = trim($text);

        // Validate channel identifier format
        if (!isValidChannelIdentifier($channel_identifier)) {
            // Clear user state if they send invalid input
            clearUserState($user_id);
            // Don't respond to invalid input - just ignore it
            return;
        }

        // Format the identifier properly
        if (is_numeric($channel_identifier) || (substr($channel_identifier, 0, 1) === '-' && is_numeric(substr($channel_identifier, 1)))) {
            // It's a numeric ID (private channel) - use as is
            $formatted_identifier = $channel_identifier;
        } else {
            // It's a username (public channel) - add @ if not present
            if (substr($channel_identifier, 0, 1) !== '@') {
                $formatted_identifier = '@' . $channel_identifier;
            } else {
                $formatted_identifier = $channel_identifier;
            }
        }

        logUserAction($user_id, 'channel_identifier_provided', "Channel identifier: $formatted_identifier");

        // Check if bot is admin in the channel
        if (!checkBotAdminStatus($formatted_identifier)) {
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ خطا!\n\n" .
                "ربات در چنل مورد نظر ادمین نیست یا چنل وجود ندارد.\n\n" .
                "لطفا:\n" .
                "1️⃣ ابتدا ربات را در چنل خود ادمین کنید\n" .
                "2️⃣ مطمئن شوید یوزرنیم یا آیدی چنل صحیح است\n" .
                "3️⃣ دوباره یوزرنیم یا آیدی چنل را ارسال کنید",
                $back_keyboard
            );
            return;
        }

        // Get channel info
        $channel_info = getChannelInfo($formatted_identifier);
        if (!$channel_info) {
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ خطا!\n\n" .
                "نمی‌توانم اطلاعات چنل مورد نظر را دریافت کنم.\n\n" .
                "لطفا یوزرنیم یا آیدی صحیح چنل را ارسال کنید.",
                $back_keyboard
            );
            return;
        }

        // Check if channel is already added (by anyone, including the current user)
        if (isChannelAlreadyAdded($channel_info->id)) {
            $existing_owner = getChannelOwner($channel_info->id);
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            if ($existing_owner == $user_id) {
                // Channel already added by the same user
                sendmessage($chat_id,
                    "❌ خطا!\n\n" .
                    "شما قبلاً این چنل را اضافه کرده‌اید.\n\n" .
                    "هر چنل فقط یک بار قابل اضافه کردن است.\n\n" .
                    "برای مدیریت چنل از منوی «چنل های من» استفاده کنید.",
                    $back_keyboard
                );
            } else {
                // Channel already added by another user
                sendmessage($chat_id,
                    "❌ خطا!\n\n" .
                    "این چنل قبلاً توسط کاربر دیگری اضافه شده است.\n\n" .
                    "هر چنل فقط می‌تواند توسط یک کاربر مدیریت شود.\n\n" .
                    "لطفا چنل دیگری را امتحان کنید.",
                    $back_keyboard
                );
            }
            return;
        }

        // Save channel info and move to next step
        setUserState($user_id, 'adding_channel_step2', [
            'identifier' => $formatted_identifier,
            'username' => $channel_info->username ?? null,
            'title' => $channel_info->title,
            'id' => $channel_info->id,
            'type' => $channel_info->type ?? 'channel'
        ]);

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        $channel_type_text = is_numeric($formatted_identifier) || (substr($formatted_identifier, 0, 1) === '-') ? 'خصوصی' : 'عمومی';

        sendmessage($chat_id,
            "✅ عالی!\n\n" .
            "چنل «{$channel_info->title}» ({$channel_type_text}) با موفقیت تأیید شد.\n\n" .
            "حالا لطفا امضای اختصاصی برای این چنل ارسال کنید:\n\n" .
            "💡 مثال: @YourChannel | کانال رسمی ما",
            $back_keyboard
        );

    } elseif ($user_state == 'adding_channel_step2') {
        // User is sending signature
        $signature = trim($text);
        $state_data = getUserStateData($user_id);

        if (empty($signature)) {
            // Clear state if empty signature is sent - ignore the message
            clearUserState($user_id);
            return;
        }

        // Check if signature is too long (more than 200 characters)
        if (strlen($signature) > 200) {
            // Clear state if signature is too long - ignore the message
            clearUserState($user_id);
            return;
        }

        // Save channel data
        $channel_data = [
            'id' => $state_data['id'],
            'identifier' => $state_data['identifier'],
            'username' => $state_data['username'],
            'title' => $state_data['title'],
            'type' => $state_data['type'],
            'signature' => $signature,
            'owner_id' => $user_id,
            'status' => 'active'
        ];

        saveChannelData($channel_data);

        // Log before clearing state
        error_log("About to clear state for user $user_id after adding channel");
        clearUserState($user_id);

        // Verify state is cleared
        $check_state = getUserState($user_id);
        error_log("State after first clear: " . ($check_state ? $check_state : 'null'));

        logUserAction($user_id, 'channel_added', "Channel added: {$state_data['identifier']} with signature: $signature");

        $main_menu_keyboard = createMainMenuKeyboard();
        sendmessage($chat_id,
            "🎉 تبریک!\n\n" .
            "چنل «{$state_data['title']}» با موفقیت اضافه شد.\n\n" .
            "📝 امضای ثبت شده: $signature\n\n" .
            "حالا می‌توانید از منوی «چنل های من» چنل خود را مشاهده کنید.",
            $main_menu_keyboard
        );

        // Clear user state again to ensure it's completely cleared
        error_log("Clearing state again for user $user_id after sending success message");
        clearUserState($user_id);

        // Final verification and force clear if needed
        $final_state = getUserState($user_id);
        error_log("Final state after second clear: " . ($final_state ? $final_state : 'null'));

        if ($final_state !== null) {
            error_log("State still exists, using force clear for user $user_id");
            forceClearUserState($user_id);
            $force_check = getUserState($user_id);
            error_log("State after force clear: " . ($force_check ? $force_check : 'null'));
        }

    } elseif ($user_state == 'editing_signature') {
        handleSignatureEditing($user_id, $chat_id, $text);
    } else {
        // If user is not in any state and didn't use a command, ignore the message
        // Don't respond to random messages when user is not in a specific state

        // Extra safety: Clear any lingering state
        if ($user_state !== null) {
            error_log("Warning: User $user_id had unexpected state '$user_state', clearing it");
            clearUserState($user_id);
        }
    }
}
?>