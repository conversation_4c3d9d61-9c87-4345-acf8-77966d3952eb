<?php

/**
 * Comment Replacer Script
 * این اسکریپت تمام کامنت‌های فایل bot.php را با یک متن ثابت جایگزین می‌کند
 */

// تنظیمات - می‌توانید اینها را تغییر دهید
$input_file = 'bot.php';
$output_file = 'bot_modified.php';
$backup_file = 'bot_backup_' . date('Y-m-d_H-i-s') . '.php';
$new_comment = '// Comment modified by script';

// تنظیمات پیشرفته
$preserve_php_doc_comments = true; // حفظ کامنت‌های PHPDoc (/** ... */)
$preserve_license_comments = true; // حفظ کامنت‌های مجوز و کپی‌رایت
$show_preview = true; // نمایش پیش‌نمایش تغییرات

// بررسی وجود فایل
if (!file_exists($input_file)) {
    die("❌ فایل $input_file یافت نشد!\n");
}

echo "🔄 شروع پردازش فایل $input_file...\n";

// ایجاد بک‌آپ
if (copy($input_file, $backup_file)) {
    echo "✅ بک‌آپ در $backup_file ایجاد شد\n";
} else {
    die("❌ خطا در ایجاد بک‌آپ!\n");
}

// خواندن محتوای فایل
$content = file_get_contents($input_file);
if ($content === false) {
    die("❌ خطا در خواندن فایل!\n");
}

echo "📊 اندازه فایل اصلی: " . strlen($content) . " بایت\n";

// شمارش کامنت‌های اصلی
$original_single_comments = preg_match_all('/\/\/.*$/m', $content);
$original_multi_comments = preg_match_all('/\/\*.*?\*\//s', $content);

echo "📝 کامنت‌های تک خطی یافت شده: $original_single_comments\n";
echo "📝 کامنت‌های چند خطی یافت شده: $original_multi_comments\n";

// پردازش هوشمند کامنت‌ها
$processed_content = $content;

// جایگزینی کامنت‌های تک خطی (// ...)
if ($preserve_license_comments) {
    // حفظ کامنت‌های حاوی کلمات کلیدی مجوز
    $license_keywords = ['copyright', 'license', 'author', '@license', '@copyright', '@author'];
    $processed_content = preg_replace_callback('/\/\/(.*)$/m', function($matches) use ($new_comment, $license_keywords) {
        $comment_text = strtolower($matches[1]);
        foreach ($license_keywords as $keyword) {
            if (strpos($comment_text, $keyword) !== false) {
                return $matches[0]; // حفظ کامنت اصلی
            }
        }
        return $new_comment;
    }, $processed_content);
} else {
    $processed_content = preg_replace('/\/\/.*$/m', $new_comment, $processed_content);
}

// جایگزینی کامنت‌های چند خطی (/* ... */)
if ($preserve_php_doc_comments) {
    // حفظ کامنت‌های PHPDoc که با /** شروع می‌شوند
    $processed_content = preg_replace_callback('/\/\*(?!\*).*?\*\//s', function($matches) use ($new_comment) {
        return $new_comment;
    }, $processed_content);
} else {
    $processed_content = preg_replace('/\/\*.*?\*\//s', $new_comment, $processed_content);
}

$content = $processed_content;

// شمارش کامنت‌های جدید برای تأیید
$new_single_comments = preg_match_all('/\/\/.*$/m', $content);
$new_multi_comments = preg_match_all('/\/\*.*?\*\//s', $content);

echo "✅ کامنت‌های تک خطی جایگزین شده: $new_single_comments\n";
echo "✅ کامنت‌های چند خطی جایگزین شده: $new_multi_comments\n";

// ذخیره فایل جدید
if (file_put_contents($output_file, $content) !== false) {
    echo "✅ فایل جدید در $output_file ذخیره شد\n";
    echo "📊 اندازه فایل جدید: " . strlen($content) . " بایت\n";
} else {
    die("❌ خطا در ذخیره فایل جدید!\n");
}

// نمایش آمار نهایی
echo "\n📈 گزارش نهایی:\n";
echo "- فایل اصلی: $input_file\n";
echo "- فایل بک‌آپ: $backup_file\n";
echo "- فایل جدید: $output_file\n";
echo "- کامنت جایگزین: '$new_comment'\n";
echo "- تعداد کل تغییرات: " . ($original_single_comments + $original_multi_comments) . "\n";

// سوال برای جایگزینی فایل اصلی
echo "\n❓ آیا می‌خواهید فایل اصلی را با فایل جدید جایگزین کنید؟ (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) == 'y' || trim(strtolower($line)) == 'yes') {
    if (copy($output_file, $input_file)) {
        echo "✅ فایل اصلی جایگزین شد\n";
        unlink($output_file); // حذف فایل موقت
        echo "🗑️ فایل موقت حذف شد\n";
    } else {
        echo "❌ خطا در جایگزینی فایل اصلی!\n";
    }
} else {
    echo "ℹ️ فایل اصلی تغییر نکرد. فایل جدید در $output_file موجود است.\n";
}

echo "\n🎉 عملیات با موفقیت انجام شد!\n";

?>
