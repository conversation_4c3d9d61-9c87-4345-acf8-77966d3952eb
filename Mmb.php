<?php

/**
 * ام ام بی، جهشی در برنامه نویسی ربات تلگرامی
 * 
 * 
 * راهنما: از ام ام بی به طور خام می توانید استفاده کنید، اما اگر میخواهید از ام ام بی 3 استفاده کنید، باید در کلاس اتم تنظیماتی را وارد کنید و سپس آن را فراخوانی کنید(تابع استارت)
 * 
 * جهت کسب اطلاعات بیشتر به سایت ام ام بی مراجعه کنید:
 * 
 * @link https://mmblib.ir
 * @version 3.1
 * @copyright 2022 Mmb
 */

/**
 * توجه: این فایل را ویرایش نکنید! زیرا باعث اختلال در روند ام ام بی می شود.
 * اگر می خواهید چیزی را شخصی سازی کنید، ام ام بی قابلیت حداکثر شخصی سازی را در اختیار شما قرار داده است :)
 */

$VERSION = '3.1';


if(
    !file_exists(__DIR__ . '/Mmb/Mmb.php') ||
    !file_exists(__DIR__ . '/Mmb/version') ||
    @file_get_contents(__DIR__ . '/Mmb/version') != $VERSION
) {
//if(true) {
    __mmb_extract();
}

define('MMB_FILE_PATH', __FILE__);
defined('BOT_DIR') || define('BOT_DIR', __DIR__);
defined('PLUGINS') || define('PLUGINS', BOT_DIR . '/plugins');
defined('MMB_DEVELOP') || define('MMB_DEVELOP', 1); // 1 = On, 0 = Off
require __DIR__ . '/Mmb/Mmb.php';





// Extract
function __mmb_extract() {
    global $VERSION;
    @mkdir("Mmb");
    $data = [
		'Mmb/403.php' => 30,
		'Mmb/Async.php' => 3922,
		'Mmb/Atom.php' => 22801,
		'Mmb/AtomInit.php' => 13725,
		'Mmb/Callback.php' => 2796,
		'Mmb/ChatUpd.php' => 4235,
		'Mmb/Chats.php' => 28372,
		'Mmb/Db.php' => 50305,
		'Mmb/Develop.php' => 11352,
		'Mmb/Exceptions.php' => 253,
		'Mmb/Helpers.php' => 19963,
		'Mmb/Inline.php' => 3817,
		'Mmb/Interface.php' => 622,
		'Mmb/Listener.php' => 10090,
		'Mmb/Mmb.php' => 58101,
		'Mmb/Msg.php' => 36192,
		'Mmb/Opis.php' => 69544,
		'Mmb/Plugin.php' => 1471,
		'Mmb/Poll.php' => 4875,
		'Mmb/Storage.php' => 14528,
		'Mmb/Tools.php' => 32440,
		'Mmb/Upd.php' => 4316,
		'Mmb/jdf.php' => 19651,
		'Mmb/.htaccess' => 78,
    ];
    $m = fopen(__FILE__, "r");
    $file = fread($m, 10000);
    $skip = strpos($file, "@Mmb" . "Lib") + 7;
    fseek($m, $skip);
    foreach($data as $dir => $len) {
        $file = fopen(__DIR__ . "/" . $dir, "w");
        stream_copy_to_stream($m, $file, $len);
        fclose($file);
    }
    fclose($m);
    file_put_contents("Mmb/version", $VERSION);
}
__halt_compiler();@MmbLib<?php echo "Access danied"; ?><?php

// Copyright (C): t.me/MMBlib

use Opis\Closure\SerializableClosure;

class Background extends MmbBase{
    
    private static $enabled = false;
    public static function isEnabled(){
        return self::$enabled;
    }

    public static function start($timeout = 3600){
        self::$enabled = true;

        header("HTTP/1.0 200 OK", true, 200);
        header("Connection: close");
        header("Content-type: text/html; charset=UTF-8");
        ob_end_clean();
        ignore_user_abort(true);
        ob_start();
        echo('Background...');
        $size = ob_get_length();
        header("Content-Length: $size");
        ob_end_flush();
        @ob_flush();
        flush();
        if (session_id()) session_write_close();
        if(function_exists('fastcgi_finish_request')) fastcgi_finish_request();

        set_time_limit($timeout);
    }

    public static function getURI(){
        return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http")
                . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    }

    public static function runBackgroundApi($url, $close = false, $delay = 0){
        $f = fopen($url, "r");
        if($delay) sleep($delay);
        if($close) fclose($f);
    }

    public static function __($_1=0,$_2=1){
        if(@$_1['background'] == 'ok'){
            if(@$_1['verify'] == md5($_2)){
                return true;
            }
            else{
                die;
            }
        }
        if(TELEGRAM_IP){
            $_1['background'] = 'ok';
            $_1['verify'] = md5($_2);
            $o = fopen((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]", 'r', false, stream_context_create([
                'http' => [
                    'method'  => 'POST',
                    'header'  => 'Content-Type: application/x-www-form-urlencoded',
                    'content' => json_encode($_1)
                ],
            ]));
            fclose($o);
            die;
        }
        else return false;
    }

    public static function runPhp($path){
        exec("php \"" . addslashes($path) . "\" >/dev/null 2>&1 &");
    }

    /**
     * اجرای تابع در پس زمینه | نیاز به اتم دارد
     *
     * توجه کنید که این تابع شبیه سازی شده است و باید چهارچوب هایی را رعایت کنید!
     * 
     * @param Closure $closure
     * @return void
     */
    public static function run(Closure $closure){
        if(!defined('MMB_ATOM'))
            throw new AtomRequiredException();
        $id = md5(time() . rand(1, 10000));
        $creative = time();
        BackgroundStorage::set('closures.' . $id, serializeClosure($closure));
        $url = self::getURI() . "?atom=background&background=closure&id=$id";
        self::runBackgroundApi($url);
    }

    public static function ____(){
        if(isset($_GET['background'])){
            $type = $_GET['background'];
            if($type == 'closure'){

                $id = $_GET['id'] ?? "";
                if(strlen($id) > 5 && strpos($id, '.') === false){
                    $closure = BackgroundStorage::get('closures.' . $id);
                    if($closure){
                        self::start();
                        require_once __DIR__ . '/Opis.php';
                        $closure = unserialize($closure);
                        if($closure instanceof SerializableClosure){
                            BackgroundStorage::unset('closures.' . $id);
                            $closure();
                            die;
                        }
                    }
                }

            }
        }
    }
}

class BackgroundStorage extends Storage{
}
<?php

define('MMB_ATOM', true);


class MSkip extends Error { }

class UsersDb{

    /**
     * گرفتن اطلاعات کاربر فعلی
     *
     * @var UserRow|null
     */
    public static $this;

    public static $__i = 0;

    /**
     * دیتای این کاربر را برمیگرداند
     * * توجه: در این تابع اگر دیتا قبلا لود شده بود، همان را بر میگرداند
     *
     * @return UserRow|false
     */
    public static function getThis(){
        if(self::$this){
            return self::$this;
        }
        else{
            if(User::$this)
                return self::$this = self::get(User::$this->id);
            else
                return false;
        }
    }

    /**
     * ساخت دیتای کاربر فعلی
     *
     * @param array $params
     * @return UserRow|false
     */
    public static function createThis($params = []){
        return self::create(User::$this->id, $params);
    }

    /**
     * ساخت دیتای کاربر
     *
     * @param string|int $id
     * @param array $params
     * @return UserRow|false
     */
    public static function create($id, $params = []){
        $current = User::$this && User::$this->id == $id;

        $params = array_replace(UserRow::defaults(), $params);
        if(!isset($params['id']))           $params['id'] = $id;
        if(!isset($params['step']))         $params['step'] = '';
        if(!isset($params['data']))         $params['data'] = '{}';
        if(!isset($params['map']))          $params['map'] = '';
        if(!isset($params['join_time']))    $params['join_time'] = time();
        if(!isset($params['invite_from']))  $params['invite_from'] = $current ? self::$__i : 0;

        
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            $ok = Atom::$db->insert('users', $params);
        }
        elseif(Atom::$db instanceof MmbJson){
            $ok = Atom::$db->insert('users', $id, $params);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
        $res = $ok ? new UserRow($params, true) : false;
        if($current) self::$this = $res;
        return $res;
    }

    /**
     * بررسی وجود دیتای کاربر
     *
     * @param string|int $id
     * @return bool
     */
    public static function exists($id){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->queryFirst('SELECT count(*) FROM `users` WHERE `id` = ? LIMIT 1', $id) ? true : false;
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->select('users', $id) ? true : false;
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * تعداد اعضای ربات
     *
     * @return int
     */
    public static function count(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->queryFirst('SELECT count(*) FROM `users`');
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->number('users');
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * گرفتن دیتای کاربر
     *
     * @param string|int $id
     * @return UserRow|false
     */
    public static function get($id){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            $res = Atom::$db->queryRow('SELECT * FROM `users` WHERE `id` = ? LIMIT 1', $id);
            if($res) return new UserRow($res);
            else return false;
        }
        elseif(Atom::$db instanceof MmbJson){
            $res = Atom::$db->select('users', $id);
            if($res) return new UserRow($res);
            else return false;
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * بروز کردن دیتای کاربر
     *
     * @param string|int $id
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function set($id, $key, $value){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->update('users', [
                $key => $value
            ], ['id' => $id]);
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->update('users', $id, [
                $key => $value
            ]);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use  or MmbSqlite or MmbJson");
        }
    }

    /**
     * بروز کردن دیتای کاربر
     *
     * @param string|int $id
     * @param array $key_value
     * @return bool
     */
    public static function update($id, array $key_value){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->update('users', $key_value, ['id' => $id]);
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->update('users', $id, $key_value);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * شناسه همه ی اعضای ربات را بصورت جتراتور بر میگرداند
     *
     * @return Generator
     */
    public static function getAll(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            foreach(Atom::$db->queryEach('SELECT `id` FROM `users`') as $row){
                yield $row['id'];
            }
        }
        elseif(Atom::$db instanceof MmbJson){
            yield from Atom::$db->selectEachNames('users');
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * اطلاعات همه ی اعضای ربات را بصورت جتراتور بر میگرداند
     *
     * @return Generator|UserRow[]
     */
    public static function getAllRows(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            foreach(Atom::$db->queryEach('SELECT * FROM `users`') as $row){
                yield new UserRow($row);
            }
        }
        elseif(Atom::$db instanceof MmbJson){
            foreach(Atom::$db->selectEach('users') as $user){
                yield new UserRow($user);
            }
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

}

if(!class_exists('UserRow')) {
    class UserRow extends UserRowBase {
        public function init2()
        {
            // None
        }

        public function getData2()
        {
            // None
        }
    }
}

if(Atom::$db && Atom::$db instanceof MmbDbBase)
    Listeners::onAtomDbInstallation(function(&$tables){
        $users = isset($tables['Users']) ? 'Users' : 'users';
        if(!isset($tables[$users])) $tables[$users] = [];
        $tables[$users]['id'] = 'bigint';
        $tables[$users]['step'] = 'text';
        $tables[$users]['data'] = 'text';
        $tables[$users]['map'] = 'text';
        $tables[$users]['join_time'] = 'int';
        $tables[$users]['invite_from'] = 'text';
    });

/**
 * دیتابیس گروه ها
 * 
 * این کلاس بصورت پیشفرض غیرفعال است
 */
class GroupsDb{

    /**
     * گرفتن اطلاعات گروه فعلی
     *
     * @var GroupRow|null
     */
    public static $this;

    public static $__i = 0;

    /**
     * دیتای این گروه را برمیگرداند
     * * توجه: در این تابع اگر دیتا قبلا لود شده بود، همان را بر میگرداند
     *
     * @return GroupRow|false
     */
    public static function getThis(){
        if(self::$this){
            return self::$this;
        }
        else{
            if(Chat::$this && (Chat::$this->type == 'group' || Chat::$this->type == 'supergroup'))
                return self::$this = self::get(Chat::$this->id);
            else
                return false;
        }
    }

    /**
     * ساخت دیتای گروه فعلی
     *
     * @param array $params
     * @return GroupRow|false
     */
    public static function createThis($params = []){
        return self::create(Chat::$this->id, $params);
    }

    /**
     * ساخت دیتای گروه
     *
     * @param string|int $id
     * @param array $params
     * @return GroupRow|false
     */
    public static function create($id, $params = []){
        $current = Chat::$this && Chat::$this->id == $id;

        $params = array_replace(GroupRow::defaults(), $params);
        $params['id'] = $id;
        if(!$params['data']) $params['data'] = "";

        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            $ok = Atom::$db->insert('groups', $params);
        }
        elseif(Atom::$db instanceof MmbJson){
            $ok = Atom::$db->insert('groups', $id, $params);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
        $res = $ok ? new GroupRow($params, true) : false;
        if($current) self::$this = $res;
        return $res;
    }

    /**
     * بررسی وجود دیتای گروه
     *
     * @param string|int $id
     * @return bool
     */
    public static function exists($id){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->queryFirst('SELECT count(*) FROM `groups` WHERE `id` = ? LIMIT 1', $id) ? true : false;
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->select('groups', $id) ? true : false;
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * تعداد گروه های ربات
     *
     * @return int
     */
    public static function count(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->queryFirst('SELECT count(*) FROM `groups`');
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->number('groups');
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * گرفتن دیتای گروه
     *
     * @param string|int $id
     * @return GroupRow|false
     */
    public static function get($id){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            $res = Atom::$db->queryRow('SELECT * FROM `groups` WHERE `id` = ? LIMIT 1', $id);
            if($res) return new GroupRow($res);
            else return false;
        }
        elseif(Atom::$db instanceof MmbJson){
            $res = Atom::$db->select('groups', $id);
            if($res) return new GroupRow($res);
            else return false;
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * بروز کردن دیتای گروه
     *
     * @param string|int $id
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function set($id, $key, $value){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->update('groups', [
                $key => $value
            ], ['id' => $id]);
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->update('groups', $id, [
                $key => $value
            ]);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * بروز کردن دیتای گروه
     *
     * @param string|int $id
     * @param array $keyVals
     * @return bool
     */
    public static function update($id, $keyVals) {
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            return Atom::$db->update('groups', $keyVals, ['id' => $id]);
        }
        elseif(Atom::$db instanceof MmbJson){
            return Atom::$db->update('groups', $id, $keyVals);
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * شناسه همه ی گروه های ربات را بصورت جتراتور بر میگرداند
     *
     * @return Generator
     */
    public static function getAll(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            foreach(Atom::$db->queryEach('SELECT `id` FROM `groups`') as $row){
                yield $row['id'];
            }
        }
        elseif(Atom::$db instanceof MmbJson){
            yield from Atom::$db->selectEachNames('groups');
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

    /**
     * اطلاعات همه ی گروه های ربات را بصورت جتراتور بر میگرداند
     *
     * @return Generator|GroupRow[]
     */
    public static function getAllRows(){
        if(Atom::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        elseif(Atom::$db instanceof MmbDbBase_Sql){
            foreach(Atom::$db->queryEach('SELECT * FROM `groups`') as $row){
                yield new GroupRow($row);
            }
        }
        elseif(Atom::$db instanceof MmbJson){
            foreach(Atom::$db->selectEach('groups') as $gp){
                yield new GroupRow($gp);
            }
        }
        else{
            throw new Exception("َAtom set database is not supported! Use MmbMySql or MmbSqlite or MmbJson");
        }
    }

}

if(!class_exists('GroupRow')) {
    class GroupRow extends GroupRowBase {

        public function init2()
        {
            // None
        }

        public function getData2()
        {
            // None
        }

    }
}

if(Atom::$db && Atom::$db instanceof MmbDbBase)
    if(Atom::$groupDbEnabled){
        Listeners::onAtomDbInstallation(function(&$tables){
            $name = isset($tables['Groups']) ? 'Groups' : 'groups';
            if(!isset($tables[$name])) $tables[$name] = [];
            $tables[$name]['id'] = 'bigint';
            $tables[$name]['data'] = 'text';
        });
    }

/**
 * پاسخ متنی | تابع کمکی
 *
 * @param string|array $text
 * @param array $args
 * @return Msg|false
 */
function replyText($text, $args = []){
    return Msg::$this->replyText($text, $args);
}

/**
 * پاسخ به کالبک | تابع کمکی
 *
 * @param string $text
 * @param bool $alert
 * @return bool
 */
function answer($text = null, $alert = false){
    return Callback::$this->answer($text, $alert);
}

/**
 * تنظیم استپ کاربر فعلی | تابع کمکی
 *
 * @param string $step
 * @param mixed $data
 * @return void
 */
function step($step, $data = null){
    if(!UsersDb::$this) return false;
    UsersDb::$this->step = $step;
    if(count(func_get_args()) > 1){
        UsersDb::$this->data = $data;
    }
    UsersDb::$this->save();
}

/**
 * بررسی برابر بودن استپ با ورودی
 *
 * @param string $step
 * @return boolean
 */
function isStep($step){
    if(!UsersDb::$this) return false;
    return UsersDb::$this->step == $step;
}


// Start
$mmb = Mmb::$this = Atom::$mmb;
if(!$mmb){
    throw new MmbException('Mmb object is not defined! Use `Atom::$mmb = $mmb = new Mmb("token");` before Atom::start().');
}
$upd = Upd::$this = $mmb->getUpd();

if($upd){
    if($upd->msg){
        define('M3_UPD_TYPE', 'msg');
        $msg = Msg::$this = $upd->msg;
        Chat::$this = $msg->chat;
        User::$this = $msg->from;
    }
    elseif($upd->editedMsg){
        define('M3_UPD_TYPE', 'editedMsg');
        $editedMsg = Msg::$this = $upd->editedMsg;
        Chat::$this = $editedMsg->chat;
        User::$this = $editedMsg->from;
    }
    elseif($upd->callback){
        define('M3_UPD_TYPE', 'callback');
        $callback = Callback::$this = $upd->callback;
        Msg::$this = $callback->msg;
        Chat::$this = $callback->msg->chat;
        User::$this = $callback->from;
    }
    elseif($upd->inline){
        define('M3_UPD_TYPE', 'inline');
        $inline = Inline::$this = $upd->inline;
        User::$this = $inline->from;
    }
    elseif($upd->post){
        define('M3_UPD_TYPE', 'post');
        $post = Msg::$this = $upd->post;
        Chat::$this = $post->chat;
    }
    elseif($upd->editedPost){
        define('M3_UPD_TYPE', 'editedPost');
        $editedPost = Msg::$this = $upd->editedPost;
        Chat::$this = $editedPost->chat;
    }
    elseif($upd->chosenInline){
        define('M3_UPD_TYPE', 'editedMsg');
        $chosenInline = ChosenInline::$this = $upd->chosenInline;
        User::$this = $chosenInline->from;
        Msg::$this = $chosenInline->msg;
    }
    elseif($upd->poll){
        define('M3_UPD_TYPE', 'poll');
        $poll = Poll::$this = $upd->poll;
    }
    elseif($upd->pollAnswer){
        define('M3_UPD_TYPE', 'pollAnswer');
        $pollAnswer = PollAnswer::$this = $upd->pollAnswer;
        User::$this = $pollAnswer->user;
    }
    elseif($upd->myChatMember){
        define('M3_UPD_TYPE', 'myChatMember');
        $myChatMember = ChatMemberUpd::$this = $upd->myChatMember;
        Chat::$this = $myChatMember->chat;
        User::$this = $myChatMember->from;
    }
    elseif($upd->chatMember){
        define('M3_UPD_TYPE', 'chatMember');
        $chatMember = ChatMemberUpd::$this = $upd->chatMember;
        Chat::$this = $chatMember->chat;
        User::$this = $chatMember->from;
    }
    else{
        // ...
    }

    if(Atom::$db && User::$this){
        UsersDb::$this = UsersDb::get(User::$this->id);
        if(!UsersDb::$this){
            if($upd->msg && $msg->started){
                UsersDb::$__i = Atom::decodeInvCode($msg->startCode);
                if(!is_numeric(UsersDb::$__i) || !UsersDb::exists(UsersDb::$__i))
                    UsersDb::$__i = 0;
            }
            if(Atom::$dbNewUser){
                if(
                    Atom::$dbNewUserIfMsg ? (
                        Atom::$dbNewUserIfMsgPV ?
                            $upd->msg && $upd->msg->chat->type == 'private' :
                            $upd->msg
                    ) : true
                ){
                    $_ = Atom::$dbNewUser;
                    $_ = $_(User::$this->id);
                    if($_ !== null) {
                        if(!UsersDb::createThis($_)) {
                            error_log("Warning: Automatic user row creation failed! Db error: " . (Atom::$db->error ?? "---"));
                        }
                    }
                }
            }
        }
    }
    if(Atom::$groupDbEnabled && Chat::$this && (Chat::$this->type == 'private' || Chat::$this->type == 'supergroup')){
        GroupsDb::$this = GroupsDb::get(Chat::$this->id);
    }

}

// API
if($_GET['atom'] ?? false){
    $atom = $_GET['atom'];
    if($atom == 'background') {
        Background::____();
    }
}

// Plugins
Plugins::startAll();
if($upd)
    if(!Listeners::__runUpd($upd)) {
        Upd::$this = false;
        Msg::$this = false;
        Callback::$this = false;
        User::$this = false;
        Chat::$this = false;
        Inline::$this = false;
        ChosenInline::$this = false;
        PollAnswer::$this = false;
        ChatMemberUpd::$this = false;
        ChatMemberUpd::$this = false;
    }


<?php

class Atom{

    /**
     * هسته ربات
     *
     * @var Mmb
     */
    public static $mmb;

    /**
     * دیتابیس
     *
     * @var MmbDbBase
     */
    public static $db;

    /**
     * محل کانفیگ دیتابیس
     *
     * @var string
     */
    public static $dbConfig = '';

    /**
     * تگ جدول دیتابیس
     * 
     * * Default: `Database`
     *
     * @var string
     */
    public static $dbConfigTag = 'Database';

    /**
     * اگر این مقدار درست باشد، تنها زمانی دیتای کاربر را خودکار می سازد، که پیامی ارسال کرده باشد
     *
     * @var boolean
     */
    public static $dbNewUserIfMsg = true;

    /**
     * اگر این مقدار و مقدار متغیر قبلی درست باشند، تنها زمانی دیتای کاربر را خودکار می سازد، که پیامی در شخصی ارسال کرده باشد
     *
     * @var boolean
     */
    public static $dbNewUserIfMsgPV = true;

    /**
     * تابعی که اطلاعات ستون کاربر را در زمان ساخته شدن برمیگرداند
     *
     * مانند:
     * * `Atom::$dbNewUser = function($id) { return [ 'score' => 0 ]; };`
     * 
     * @var Closure
     */
    public static $dbNewUser;

    /**
     * تابع ساخت کد لینک دعوت با شناسه کاربر
     * `function($id) { return "inv-$id" }`
     *
     * @var Callable|Closure|false
     */
    public static $invLinkEn = false;

    /**
     * تابع تبدیل کد لینک دعوت به شناسه کاربر
     * `function($code) { if(@substr($code, 0, 4) == 'inv-') return substr($code, 4); else return false; }`
     *
     * @var Callable|Closure|false
     */
    public static $invLinkDe = false;

    /**
     * محل قرار گیری حافظه
     *
     * @var string
     */
    public static $storage = __DIR__ . '/Storage';

    public static function installationDb(){
        if(self::$db == null){
            throw new MmbException('Atom database not set! Use `Atom::$db = ...;` before Atom::start().');
        }
        if(self::$dbConfig == null){
            throw new MmbException('Atom database config not set! Use `Atom::$dbConfig = ...;` before Atom::start().');
        }
        if(self::$db instanceof MmbDbBase_Sql)
            return self::$db->installation(self::$dbConfig, self::$dbConfigTag);
        elseif(self::$db instanceof MmbJson)
            return self::$db->installation(self::$dbConfig, self::$dbConfigTag);
        return "Database does not support installation!";
    }

    /**
     * ساخت کد دعوت
     *
     * @param string $id
     * @return string
     */
    public static function createInvCode($id){
        if(self::$invLinkEn){
            $v = self::$invLinkEn;
            return urlencode($v($id));
        }
        else{
            return urlencode($id);
        }
    }

    public static function decodeInvCode($code){
        if(self::$invLinkEn){
            $v = self::$invLinkDe;
            return $v($code);
        }
        else{
            return $code;
        }
    }

    /**
     * یوزرنیم ربات - بهتر است بدون @ وارد شود
     *
     * @var string
     */
    public static $botUsername;
    /**
     * ساخت لینک دعوت
     *
     * @param string $id
     * @return string
     */
    public static function createInvLink($id = null){
        if($id === null && User::$this) $id = User::$this->id;
        $un = self::$botUsername;
        $un = trim(str_replace('@', '', $un));
        $code = self::createInvCode($id);
        return "https://t.me/$un?start=$code";
    }


    /**
     * با این تابع اتم ام ام بی را روشن می کنید! توجه کنید که برای استفاده از اتم، باید تنظیمات لازم را وارد کرده باشید
     *
     * @return void
     */
    public static function start(){
        require_once __DIR__ . '/Atom.php';
        Listeners::__runStart();
    }

    /**
     * فعال بودن یا نبودن دیتابیس گروه
     *
     * @var boolean
     */
    public static $groupDbEnabled = false;
    
    /**
     * با استفاده از این تابع، کلاس دیتابیس گروه را فعال می کنید
     *
     * @return void
     */
    public static function enableGroupDb(){
        self::$groupDbEnabled = true;
    }

    /**
     * زمان شروع فعالیت سورس
     *
     * @var float
     */
    public static $runTime;
    private static $_run_long = false;
    /**
     * بررسی طولانی بودن پروسه
     *
     * @return bool
     */
    public static function runIsLong() {
        if(self::$_run_long) return true;
        if(microtime(true) - self::$runTime >= 2) {
            self::$_run_long = true;
            return true;
        }
        return false;
    }
}
Atom::$runTime = microtime(true);


abstract class DbRowBase implements JsonSerializable {

    /**
     * گرفتن مقدار های پیشفرض در زمان ساخت ردیف جدید
     *
     * @return array
     */
    public static function defaults() {
        return [];
    }

    /**
     * ذخیره تغییرات ردیف
     *
     * @param bool $saveChanges در صورت درست بودن، فقط تغییرات ذخیره می شوند
     * @return void
     */
    public function save($saveChanges = true) {
        $data = $this->getFullData();

        $last_data = $this->_last_data;
        $this->_last_data = $data;

        if($saveChanges) {
            foreach($data as $name => $value) {
                if($value == @$last_data[$name]) {
                    unset($data[$name]);
                }
            }
        }

        if($data) {
            $this->updateInDb($data);
        }
    }

    /**
     * ذخیره تغییرات ردیف، همراه با فیلتر
     *
     * @param bool $saveChanges در صورت درست بودن، فقط تغییرات ذخیره می شوند
     * @return void
     */
    public function save2(array $cols, $saveChanges = true) {
        $data0 = $this->getFullData();
        $data = [];

        $last_data = $this->_last_data;
        foreach($cols as $col) {
            $data[$col] = $data0[$col];
            $this->_last_data[$col] = $data0[$col];
        }

        if($saveChanges) {
            foreach($data as $name => $value) {
                if($value == @$last_data[$name]) {
                    unset($data[$name]);
                }
            }
        }

        if($data) {
            $this->updateInDb($data);
        }
    }

    /**
     * اگر این کلاس با تابع کریت ایجاد شده باشد، درست خواهد بود
     *
     * @var boolean
     */
    public $is_new = false;

    private $_last_data;
    private $_keys;
    public function __construct($data, $is_new = false)
    {
        $this->is_new = $is_new;
        $this->_last_data = $data;
        $this->_keys = array_keys($data);
        foreach($data as $name => $value) {
            $this->$name = $value;
        }
        $this->init();
    }
    protected final function __construct2($data) {
        $this->_last_data = $data;
        $this->_keys = array_keys($data);
        foreach($data as $name => $value) {
            $this->$name = $value;
        }

    }

    public function jsonSerialize()
    {
        return $this->getFullData();
    }

    public final function getFullData() {
        $data = $this->getData() ?: [];
        foreach($this->_keys as $key) {
            if(!isset($data[$key]))
                $data[$key] = $this->$key;
        }
        return $data;
    }


    /**
     * شروع کلاس
     *
     * @return void
     */
    public abstract function init();
    /**
     * گرفتن آرایه ای از نام و مقدار های ردیف برای ذخیره در دیتابیس
     *
     * @return array
     */
    public abstract function getData();
    /**
     * بروز کردن این ردیف در دیتابیس
     *
     * @param array $data
     * @return void
     */
    public abstract function updateInDb($data);

}

abstract class UserRowBase extends DbRowBase {

    /**
     * شناسه کاربر
     *
     * @var int
     */
    public $id;
    /**
     * استپ کاربر
     *
     * @var string
     */
    public $step;
    /**
     * دیتای مخصوص کاربر | این مقدار با جیسون انکد و دیکد می شود
     *
     * @var mixed|array
     */
    public $data;
    /**
     * دعوت شده از طرف کاربر
     *
     * @var int
     */
    public $invite_from;
    private $inviteConfirmed;
    /**
     * زمان یونیکس عضویت کاربر
     *
     * @var int
     */
    public $join_time;

    
    /**
     * بررسی می کند کاربر با لینک کسی دعوت شده بوده و هنوز تایید نشده
     *
     * @return bool
     */
    public function invCheck(){
        if($this->invite_from) {
            return !$this->inviteConfirmed;
        }
        else {
            return false;
        }
    }

    /**
     * تایید کردن دعوت شده
     *
     * @return void
     */
    public function invConfirm(){
        if($this->invite_from) {
            $this->inviteConfirmed = true;
            $i = $this->getData()['invite_from'];
            $this->updateInDb([
                'invite_from' => $i
            ]);
        }
    }


    public final function init()
    {
        $this->data = @json_decode($this->data, true);
        if($p = strpos($this->invite_from, '+')) {
            $this->invite_from = substr($this->invite_from, 0, $p);
            $this->inviteConfirmed = true;
        }
        else {
            $this->inviteConfirmed = false;
        }
        $this->init2();
    }

    /**
     * شروع کلاس - مقداردهی
     *
     * @return void
     */
    public abstract function init2();

    public final function getData()
    {
        return array_replace([
            'data' => json_encode($this->data),
            'invite_from' => $this->invite_from . ($this->inviteConfirmed ? '+' : '')
        ], $this->getData2() ?: []);
    }

    /**
     * گرفتن آرایه ای از نام و مقدار های ردیف برای ذخیره در دیتابیس
     * 
     * توجه: در این تابع متغیر های اصلی ام ام بی نیاز نیستند
     *
     * @return array
     */
    public abstract function getData2();

    public final function updateInDb($data)
    {
        UsersDb::update($this->id, $data);
    }

}

abstract class GroupRowBase extends DbRowBase {

    /**
     * شناسه گروه
     *
     * @var int
     */
    public $id;
    /**
     * دیتای مخصوص گروه | این مقدار با جیسون انکد و دیکد می شود
     *
     * @var mixed|array
     */
    public $data;

    
    public final function init()
    {
        $this->data = @json_decode($this->data, true);
        $this->init2();
    }

    /**
     * شروع کلاس - مقداردهی
     *
     * @return void
     */
    public abstract function init2();

    public final function getData()
    {
        return array_replace([
            'data' => json_encode($this->data)
        ], $this->getData2() ?: []);
    }

    /**
     * گرفتن آرایه ای از نام و مقدار های ردیف برای ذخیره در دیتابیس
     * 
     * توجه: در این تابع متغیر های اصلی ام ام بی نیاز نیستند
     *
     * @return array
     */
    public abstract function getData2();

    public final function updateInDb($data)
    {
        GroupsDb::update($this->id, $data);
    }

}

abstract class AdminPerBase extends DbRowBase {

    /**
     * شناسه ادمین
     *
     * @var int
     */
    public $id;

    public $_can_save;

    /**
     * @param string|int $id
     * @param array|string $params
     */
    public function __construct($id, $pers, $can_save = true)
    {
        $this->id = $id;
        $this->_can_save = $can_save;
        if($pers instanceof AdminPerBase) $pers = $pers->toArray();
        if(is_array($pers)) {
            $this->__construct2($pers);
            $this->init();
        }
        else {
            $this->__construct2([]);
            $this->initAs($pers);
        }
    }

    /**
     * زمانی که دسترسی ها بصورت غیر آرایه وارد می شوند این متد صدا زده می شود
     * 
     * دسترسی هایی مثل * را می توان شخصی سازی کرد(هیچ کدی از طرف ام ام بی تعریف نشده است)
     *
     * @param string $as
     * @return void
     */
    public abstract function initAs($as);

    public function toArray() {
        return $this->getFullData();
    }

    public function updateInDb($data)
    {
        if($this->_can_save)
            Admins::update($this->id, $data);
    }

}
<?php

// Copyright (C): t.me/MMBlib

class Callback extends MmbBase implements IMsg, IUser, IChat{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var MMB
     */
    private $_base;
    /**
     * From user
     * از طرف کاربر
     *
     * @var user
     */
    public $from;
    /**
     * Message or fake message(for inline mode)
     * پیام اصلی، یا پیام فیک(بدون اطلاعات، فقط جهت استفاده از توابع) در حالت اینلاین
     *
     * @var msg
     */
    public $msg;
    /**
     * Callback button data
     * دیتای دکمه
     *
     * @var string
     */
    public $data;
    /**
     * Callback query id
     * آیدی کالبک
     *
     * @var string
     */
    public $id;
    /**
     * Is inline message
     * آیا پیام مربوط به حالت اینلاین است
     *
     * @var bool
     */
    public $isInline;
    function __construct($cl, $base){
        $this->_base = $base;
        $this->from = new user($cl['from'], $base);
        $this->data = $cl['data'];
        $this->id = $cl['id'];
        if(isset($cl['message'])){
            $this->msg = new msg($cl['message'], $base);
            $this->isInline = false;
        }
        if(isset($cl['inline_message_id'])){
            $this->isInline = true;
            $this->msg = new msg($cl['inline_message_id'], $base, true);
            /*$this->msg->isInline = true;
            $this->msg->inlineID = $cl['inline_message_id'];*/
        }
    }
    
    /**
     * Answer callback query
     * پاسخ به کالبک (نمایش پیغام و پایان دادن به انتظار تلگرام)
     * اگر شما از این تابع در کالبک های خود استفاده نکنید، در صورت استفاده ی زیاد از کالبک های ربات شما، تلگرام به شما اخطاری می دهد که پاسخ به کالبک ها بسیار طول می کشد!
     *
     * @param string $text
     * @param bool $alert Show alert | نمایش پنجره هنگام نمایش 
     * @return bool
     */
    function answer($text = null, $alert = false){
        if(is_array($text)){
            $text['id'] = $this->id;
            return $this->_base->answerCallback($text);
        }
        return $this->_base->answerCallback(['id'=>$this->id, 'text'=>$text, 'alert'=>$text ? $alert : null]);
    }

    public function __getChatID()
    {
        return $this->msg->chat->id;
    }

    public function __getUserID()
    {
        return $this->from->id;
    }

    public function __getMsgID()
    {
        return $this->msg->id;
    }
}
<?php

// Copyright (C): t.me/MMBlib

class ChatMemberUpd extends MmbBase implements IChat, IUser{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var Mmb
     */
    private $_base;

    /**
     * Chat
     * چت
     *
     * @var Chat
     */
    public $chat;
    /**
     * User
     * کاربر
     *
     * @var User
     */
    public $from;
    /**
     * Date
     * تاریخ
     *
     * @var int
     */
    public $date;
    /**
     * Old chat member status
     * وضعیت قدیمی کاربر
     *
     * @var ChatMember
     */
    public $old;
    /**
     * New chat member status
     * وضعیت جدید کاربر
     *
     * @var ChatMember
     */
    public $new;
    /**
     * Invite link
     * لینکی که کاربر با آن دعونت شده
     *
     * @var ChatInvite
     */
    public $inviteLink;

    /**
     * Is private chat
     * آیا چت خصوصی است
     *
     * @var bool
     */
    public $isPrivate;
    /**
     * آیا کاربر ربات را شروع کرد
     *
     * @var bool
     */
    public $isStart;
    /**
     * آیا کاربر ربات را بلاک کرد
     *
     * @var bool
     */
    public $isStop;

    public function __construct($a, $base)
    {
        $this->_base = $base;
        $this->chat = new Chat($a['chat'], $base);
        $this->from = new User($a['from'], $base);
        $this->date = $a['date'];
        $this->old = new ChatMember($a['old_chat_member'], $base);
        $this->new = new ChatMember($a['new_chat_member'], $base);
        if($_ = $a['invite_link'])
            $this->inviteLink = new ChatInvite($_, $this->chat->id, $base);

        $this->isPrivate = $this->chat->type == Chat::TYPE_PRIVATE;
        if($this->isPrivate){
            $this->isStart = $this->old->status == ChatMember::STATUS_KICKED &&
                                $this->new->status == ChatMember::STATUS_MEMBER;
            $this->isStop = $this->old->status == ChatMember::STATUS_MEMBER &&
                                $this->new->status == ChatMember::STATUS_KICKED;
        }
        else{
            $this->isStart = false;
            $this->isStop = false;
        }
    }

    public function __getChatID()
    {
        return $this->chat->id;
    }

    public function __getUserID()
    {
        return $this->from->id;
    }

}

class JoinReq extends MmbBase implements IChat, IUser{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var Mmb
     */
    private $_base;

    /**
     * Chat
     * چت
     *
     * @var Chat
     */
    public $chat;
    /**
     * User
     * کاربر
     *
     * @var User
     */
    public $from;
    /**
     * Date
     * تاریخ
     *
     * @var int
     */
    public $date;
    /**
     * User bio
     * بیوگرافی کاربر
     *
     * @var string
     */
    public $bio;
    /**
     * Invite link
     * لینک دعوت
     *
     * @var ChatInvite
     */
    public $inviteLink;

    public function __construct($a, $base)
    {
        $this->_base = $base;
        $this->chat = new Chat($a['chat'], $base);
        $this->from = new User($a['from'], $base);
        $this->date = $a['date'];
        $this->bio = @$a['bio'];
        if($_ = $a['invite_link'])
            $this->inviteLink = new ChatInvite($_, $this->chat->id, $base);
    }

    /**
     * Approve
     * تایید درخواست عضویت
     *
     * @return bool
     */
    public function approve(){
        return $this->_base->approveJoinReq($this->chat->id, $this->from->id);
    }

    /**
     * Decline
     * رد کردن درخواست عضویت
     *
     * @return bool
     */
    public function decline(){
        return $this->_base->declineJoinReq($this->chat->id, $this->from->id);
    }
    
    public function __getChatID()
    {
        return $this->chat->id;
    }

    public function __getUserID()
    {
        return $this->from->id;
    }
    
}
<?php

// Copyright (C): t.me/MMBlib

class User extends MmbBase implements IChat, IUser{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * User id
     * آیدی کاربر
     *
     * @var int
     */
    public $id;
    /**
     * First name
     * نام کوچک
     *
     * @var string
     */
    public $firstName;
    /**
     * Last name
     * نام بزرگ
     *
     * @var string|null
     */
    public $lastName;
    /**
     * Full name
     * نام کامل
     *
     * @var string
     */
    public $name;
    /**
     * Username
     * یوزرنیم
     *
     * @var string
     */
    public $username;
    /**
     * Is bot?
     * ربات بودن شخص
     *
     * @var bool
     */
    public $isBot;
    /**
     * Language code
     * کد زبان
     *
     * @var string
     */
    public $lang;
    /**
     * @var MMB
     */
    private $_base;
    function __construct($f, $base){
        $this->_base = $base;
        $this->id = $f['id'];
        $this->firstName = $this->first_name = $f['first_name'];
        $this->lastName = $this->last_name = @$f['last_name'];
        $this->name = $f['first_name'].(isset($f['last_name']) ? " ".$f['last_name'] : "");
        $this->username = @$f['username'];
        $this->isBot = $this->bot = @$f['is_bot'];
        $this->lang = @$f['language_code'];
    }
    
    /**
     * Get user profile photos
     * گرفتن تصاویر پروفایل کاربر
     *
     * @param int $offset
     * @param int $limit
     * @return userProfs|null
     */
    function getProfs($offset=null, $limit=null){
        return $this->_base->getUserProfs($this->id, $offset, $limit);
    }
    
    /**
     * Get user data
     * گرفتن دیتای کاربر
     *
     * @return mixed
     */
    function getData(){
        return $this->_base->getData($this->id);
    }
    /**
     * Set user data
     * تنظیم دیتای کاربر
     *
     * @return bool
     */
    function setData($data){
        return $this->_base->setData($this->id, $data);
    }
    /**
     * Check exists user data
     * بررسی موجودیت دیتای کاربر
     *
     * @return bool
     */
    function exData(){
        return $this->_base->exData($this->id);
    }

    /**
     * Get user status in chat
     * گرفتن وضعیت کاربر در چت
     *
     * @param mixed $chat
     * @return ChatMember|bool
     */
    public function getMember($chat){
        if(!is_array($chat))
            $chat = ['chat' => $chat];
        $chat['user'] = $this->id;
        return $this->_base->getChatMember($chat);
    }

    /**
     * Send message to user
     * ارسال پیام به کاربر
     *
     * @param string|array $text
     * @param array $args
     * @return Msg|false
     */
    function sendMsg($text, $args = []){
        if(gettype($text) == "array"){
            $args = array_merge($text, $args);
        }else{
            $args['text'] = $text;
        }
        $args['id'] = $this->id;
        return $this->_base->sendMsg($args);
    }
    
    /**
     * Send x message
     * ارسال پیام به کاربر با ارسال پیامی با نوع x
     *
     * @param string|array $type
     * @param array $args
     * @return msg|false
     */
    function send($type, $args=[]){
        if(gettype($type) == "array"){
            $args = array_merge($type, $args);
            $type = @$args['type'];
            unset($args['type']);
        }
        $args['id'] = $this->id;
        return $this->_base->send($type, $args);
    }

    public function __getChatID()
    {
        return $this->id;
    }

    public function __getUserID()
    {
        return $this->id;
    }
    
}

class Chat extends MmbBase implements IChat{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * Chat id
     * آیدی چت
     *
     * @var int
     */
    public $id;
    /**
     * Chat type
     * نوع چت
     *
     * @var string
     */
    public $type;
    public const TYPE_PRIVATE = 'private';
    public const TYPE_GROUP = 'group';
    public const TYPE_SUPERGROUP = 'supergroup';
    public const TYPE_CHANNEL = 'channel';

    /**
     * Chat title
     * عنوان چت
     *
     * @var string|null
     */
    public $title;

    /**
     * Chat username
     * نام کاربری چت
     *
     * @var string|null
     */
    public $username;
    /**
     * First name
     * نام کوچک
     *
     * @var string|null
     */
    public $firstName;
    /**
     * Last name
     * نام بزرگ
     *
     * @var string|null
     */
    public $lastName;
    /**
     * Full namd
     * نام کامل
     *
     * @var string
     */
    public $name;
    /**
     * Bio or description
     * بیوگرافی کاربر یا گروه یا کانال
     *
     * @var string|null
     */
    public $bio;
    /**
     * Photo
     * عکس پروفایل
     *
     * @var ChatPhoto|null
     */
    public $photo;
    /**
     * Invite link
     * لینک دعوت
     *
     * @var string|null
     */
    public $inviteLink;
    /**
     * Pinned message
     * پیغام سنجاق شده در چت
     *
     * @var Msg|null
     */
    public $pinnedMsg;
    /**
     * Slow mode delay
     * تاخیر حالت آهسته
     *
     * @var int|null
     */
    public $slowDelay;
    /**
     * Linked chat id
     * آیدی گروه یا کانال متصل به چت
     *
     * @var int|null
     */
    public $linkedChatID;
    /**
     * دسترسی های گروه
     * 
     * تنها در تابع گت چت مقدار داده می شود
     *
     * @var ChatPer|null
     */
    public $pers;
    /**
     * @var MMB
     */
    private $_base;
    function __construct($c, $base){
        $this->_base = $base;
        $this->id = $c['id'];
        $this->type = @$c['type'];
        $this->title = @$c['title'];
        $this->username = @$c['username'];
        $this->firstName = @$c['first_name'];
        $this->lastName = @$c['last_name'];
        $this->name = $this->firstName . ($this->lastName ? ' ' . $this->lastName : '');
        $this->bio = @$c['bio'];
        if(@$c['des'])
            $this->bio = @$c['des'];
        if($_ = @$c['photo'])
            $this->photo = new ChatPhoto($_, $base);
        $this->inviteLink = @$c['invite_link'];
        $this->pinnedMsg = @$c['pinned_message'];
        $this->slowDelay = @$c['slow_mode_delay'];
        $this->linkedChatID = @$c['linked_chat_id'];
        if(isset($c['permissions'])){
            $this->pers = new ChatPer($c['permissions'], null, $base);
        }
    }
    
    /**
     * Get chat member
     * گرفتن اطلاعات کاربر در چت
     *
     * @param mixed $user
     * @return ChatMember|false
     */
    function getMember($user){
        if(is_array($user)){
            $user['chat'] = $this->id;
            return $this->_base->getChatMember($user);
        }
        return $this->_base->getChatMember($this->id, $user);
    }
    
    /**
     * Get chat members count
     * گرفتن تعداد عضو های چت
     *
     * @return int|false
     */
    function getMemberNum(){
        return $this->_base->getChatMemberNum($this->id);
    }
    
    /**
     * Get chat members count
     * گرفتن تعداد عضو های چت
     *
     * @return int|false
     */
    function getMemberCount(){
        return $this->_base->getChatMemberCount($this->id);
    }
    
    /**
     * Ban member
     * حذف کاربر از چت
     *
     * @param mixed $user
     * @param int $until
     * @return bool|false
     */
    function ban($user, $until=null){
        if(is_array($user)){
            $user['chat'] = $this->id;
            return $this->_base->ban($user);
        }
        return $this->_base->ban($this->id, $user, $until);
    }
    
    /**
     * Unban member
     * رفع مسدودیت کاربر از چت
     *
     * @param mixed $user
     * @return bool|false
     */
    function unban($user){
        if(is_array($user)){
            $user['chat'] = $this->id;
            return $this->_base->unban($user);
        }
        return $this->_base->unban($this->id, $user);
    }
    
    /**
     * Restrict user
     * محدود کردن کاربر
     *
     * @param mixed $user
     * @param array $per
     * @param int $until
     * @return bool
     */
    function restrict($user, $per=[], $until=null){
        if(is_array($user)){
            $user['chat'] = $this->id;
            return $this->_base->restrict($user);
        }
        return $this->_base->restrict($this->id, $user, $per, $until);
    }
    
    /**
     * Promote user
     * ترفیع کاربر
     *
     * @param mixed $user
     * @param array $per
     * @return bool
     */
    function promote($user, $per=[]){
        if(is_array($user)){
            $user['chat'] = $this->id;
            return $this->_base->promote($user);
        }
        return $this->_base->promote($this->id, $user, $per);
    }
    
    /**
     * Set chat permissions
     * تنظیم دسترسی های گروه
     *
     * @param array $per
     * @return bool
     */
    function setPer($per){
        if($per instanceof JsonSerializable) $per = $per->jsonSerialize();
        if(isset($per['per'])){
            $per['chat'] = $this->id;
            return $this->_base->setChatPer($per);
        }
        return $this->_base->setChatPer($this->id, $per);
    }
    
    /**
     * Get chat invite link
     * گرفتن لینک دعوت چت
     *
     * @return string
     */
    function getInviteLink(){
        return $this->_base->getInviteLink($this->id);
    }
    
    /**
     * Create invite link
     * ساخت لینک دعوت
     * [chat-name-expire-limit-joinReq]
     *
     * @param array $args
     * @return ChatInvite|false
     */
    function createInviteLink($args){
        $args['chat'] = $this->id;
        return $this->createInviteLink($args);
    }

    /**
     * Edit invite link
     * ویرایش لینک دعوت
     * [chat-link-name-expire-limit-joinReq]
     *
     * @param array $args
     * @return ChatInvite|false
     */
    public function editInviteLink($args){
        $args['chat'] = $this->id;
        return $this->editInviteLink($args);
    }
    
    /**
     * Set chat photo
     * تنظیم عکس چت
     *
     * @param mixed $photo
     * @return bool
     */
    function setPhoto($photo){
        if(is_array($photo)){
            $photo['id'] = $this->id;
            return $this->_base->setChatPhoto($photo);
        }
        return $this->_base->setChatPhoto($this->id, $photo);
    }
    
    /**
     * Delete chat photo
     * حذف عکس چت
     *
     * @return bool
     */
    function delPhoto(){
        return $this->_base->delChatPhoto($this->id);
    }
    
    /**
     * Set chat title
     * تنظیم عنوان چت
     *
     * @param string $title
     * @return bool
     */
    function setTitle($title){
        if(is_array($title)){
            $title['chat'] = $this->id;
            return $this->_base->setChatTitle($title);
        }
        return $this->_base->setChatTitle($this->id, $title);
    }
    
    /**
     * Set chat description
     * تنظیم توضیحات گروه
     *
     * @param string $des Description | توضیحات
     * @return bool
     */
    function setDes($des){
        if(is_array($des)){
            $des['chat'] = $this->id;
            return $this->_base->setChatDes($des);
        }
        return $this->_base->setChatDes($this->id, $des);
    }
    
    /**
     * Pin message
     * سنجاق کردن پیام
     *
     * @param mixed $msg Message id or message object | آیدی یا شئ پیام
     * @return bool
     */
    function pin($msg){
        if(is_array($msg)){
            $msg['chat'] = $this->id;
            return $this->_base->pinMsg($msg);
        }
        return $this->_base->pinMsg($this->id, $msg);
    }
    
    /**
     * Unpin message
     * حذف سنجاق پیام
     *
     * @param mixed $msg
     * @return bool
     */
    function unpin($msg = null){
        if(is_array($msg)){
            $msg['chat'] = $this->id;
            return $this->_base->unpinMsg($msg);
        }
        return $this->_base->unpinMsg($this->id, $msg);
    }
    
    /**
     * Unpin all pinned messages
     * حذف سنجاق تمامی پیام های سنجاق شده
     *
     * @param mixed $msg
     * @return bool
     */
    function unpinAll(){
        return $this->_base->unpinAll($this->id);
    }
    
    /**
     * Leave chat
     * ترک چت
     *
     * @return bool
     */
    function leave(){
        return $this->_base->leave($this->id);
    }
    
    /**
     * Get admins list
     * گرفتن لیست ادمین ها
     *
     * @return ChatMember[]|false
     */
    function getAdmins(){
        return $this->_base->getChatAdmins($this->id);
    }
    
    function setStickerSet($setName){
        if(is_array($setName)){
            $setName['chat'] = $this->id;
            return $this->_base->setChatStickerSet($setName);
        }
        return $this->_base->setChatStickerSet($this->id, $setName);
    }
    
    function delStickerSet(){
        return $this->_base->delChatStickerSet($this->id);
    }

    /**
     * Send chat action
     * ارسال حالت چت
     *
     * @param mixed $action
     * @return bool
     */
    function action($action){
        if(gettype($action)=="array")
            return $this->_base->call('sendchataction', $action);
        return $this->_base->call('sendchataction', ['id'=>$this->id, 'action'=>$action]);
    }

    /**
     * Send message to chat
     * ارسال پیام به چت
     *
     * @param string|array $text
     * @param array $args
     * @return Msg|false
     */
    function sendMsg($text, $args = []){
        if(gettype($text) == "array"){
            $args = array_merge($text, $args);
        }else{
            $args['text'] = $text;
        }
        $args['id'] = $this->id;
        return $this->_base->sendMsg($args);
    }
    
    /**
     * Send x message
     * ارسال پیام به چت با ارسال پیامی با نوع x
     *
     * @param string|array $type
     * @param array $args
     * @return msg|false
     */
    function send($type, $args=[]){
        if(gettype($type) == "array"){
            $args = array_merge($type, $args);
            $type = @$args['type'];
            unset($args['type']);
        }
        $args['id'] = $this->id;
        return $this->_base->send($type, $args);
    }

    public function __getChatID()
    {
        return $this->id;
    }

}

class UserInChat extends MmbBase implements IChat, IUser{
    /**
     * @var MMB
     */
    private $_base;
    /**
     * کاربر هدف
     *
     * @var mixed
     */
    private $user;
    /**
     * چت هدف
     *
     * @var mixed
     */
    private $chat;
    function __construct($user, $chat, $base){
        $this->_base = $base;
        $this->user = $user;
        if($user instanceof IUser)
            $this->user = $user->__getUserID();
        $this->chat = $chat;
        if($chat instanceof IChat)
            $this->chat = $chat->__getChatID();
    }
    
    /**
     * Get chat member
     * گرفتن اطلاعات کاربر در چت
     *
     * @return chatMember
     */
    function getMember(){
        return $this->_base->getChatMember($this->chat, $this->user);
    }
    
    /**
     * Kick user
     * حذف کاربر از گروه|کانال
     *
     * @param int $until
     * @return bool
     */
    function kick($until=null){
        return $this->_base->kick($this->chat, $this->user, $until);
    }
    /**
     * Ban user
     * حذف کاربر از گروه|کانال
     *
     * @param int $until
     * @return bool
     */
    function ban($until=null){
        if(is_array($until)){
            $until['chat'] = $this->chat;
            $until['user'] = $this->user;
            return $this->_base->ban($until);
        }
        return $this->_base->ban($this->chat, $this->user, $until);
    }
    
    /**
     * Unban user
     * رفع مسدودیت کاربر در گروه|کانال
     *
     * @return bool
     */
    function unban(){
        return $this->_base->unban($this->chat, $this->user);
    }
    
    /**
     * Restrict user
     * محدود کردن کاربر
     *
     * @param array $per
     * @param int $until
     * @return bool
     */
    function restrict($per = [], $until = null){
        if(isset($per['per'])){
            $per['chat'] = $this->chat;
            $per['user'] = $this->user;
            return $this->_base->restrict($per);
        }
        return $this->_base->restrict($this->chat, $this->user, $per, $until);
    }
    
    /**
     * Promote user
     * ترفیع دادن به کاربر
     *
     * @param array $per
     * @return bool
     */
    function promote($per = []){
        if(isset($per['per'])){
            $per['chat'] = $this->chat;
            $per['user'] = $this->user;
            return $this->_base->promote($per);
        }
        return $this->_base->promote($this->chat, $this->user, $per);
    }

    public function __getChatID()
    {
        return $this->chat;
    }

    public function __getUserID()
    {
        return $this->user;
    }

}

class UserProfs extends MmbBase{
    /**
     * Photos
     * عکس ها
     *
     * @var MsgData[][]
     */
    public $photos;

    /**
     * Total count
     * تعداد کل
     *
     * @var int
     */
    public $count;

    /**
     * @var MMB
     */
    private $_base;
    function __construct($v, $base){
        $this->_base = $base;
        $this->count = $v['total_count'];
        $this->photos = [];
        foreach($v['photos']as$once){
            $a=[];
            foreach($once as $x)
                $a[] = new msgData("photo", $x, $base);
            $this->photos[] = $a;
        }
    }
}

class ChatPhoto extends MmbBase{
    /**
     * Small photo (160 * 160)
     * تصویر کوچک (160 * 160)
     *
     * @var MsgData
     */
    public $small;
    /**
     * Big photo (640 * 640)
     * تصویر بزرگ (640 * 640)
     *
     * @var MsgData
     */
    public $big;
    /**
     * @var MMB
     */
    private $_base;
    function __construct($v, $base){
        $this->_base = $base;
        $this->small = new msgData("photo", ['file_id'=>$v['small_file_id'], 'width'=>160, 'height'=>160], $base);
        $this->big = new msgData("photo", ['file_id'=>$v['big_file_id'], 'width'=>640, 'height'=>640], $base);
    }
}

class ChatInvite extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * Invite link
     * لینک دعوت
     *
     * @var string
     */
    public $link;

    /**
     * Chat
     * چت - داده ی نا امن
     *
     * @var mixed
     */
    public $chatLink;

    /**
     * Creator
     * سازنده لینک
     *
     * @var User
     */
    public $creator;

    /**
     * Creates join request
     * عضویت با تایید
     *
     * @var bool
     */
    public $joinReq;

    /**
     * Is primary
     *
     * @var bool
     */
    public $primary;

    /**
     * Is revoked
     * آیا لینک باطل شده است
     *
     * @var bool
     */
    public $revoked;

    /**
     * Name
     * اسم
     * 
     * @var string
     */
    public $name;

    /**
     * Expire date
     * تاریخ انقضا
     *
     * @var int
     */
    public $expire;

    /**
     * Member limit
     * محدودیت تعداد
     *
     * @var int
     */
    public $limit;

    /**
     * Pending join requests count
     * تعداد کاربران منتظر برای تایید
     *
     * @var int
     */
    public $pendings;

    public function __construct($inv, $chat, $base){
        $this->_base = $base;
        $this->link = $inv['invite_link'];
        $this->chatLink = $chat;
        $this->creator = new User($inv['creator'], $base);
        $this->primary = $inv['is_primary'];
        $this->revoked = $inv['is_revoked'];
        $this->name = @$inv['name'];
        $this->expire = @$inv['expire_date'];
        $this->limit = @$inv['member_limit'];
        $this->pendings = @$inv['pending_join_request_count'];
    }

    /**
     * Edit link
     * ویرایش لینک دعوت
     *
     * @param array $args
     * @return ChatInvite|false
     */
    public function edit($args){
        if(!$args['chat'])
            $args['chat'] = $this->chatLink;
        $args['link'] = $this->link;
        return $this->_base->editInviteLink($args);
    }

    /**
     * Edit link
     * ویرایش لینک دعوت
     *
     * @param array $args
     * @return ChatInvite|false
     */
    public function revoke(){
        $args = [
            'chat' => $this->chatLink,
            'link' => $this->link
        ];
        return $this->_base->revokeInviteLink($args);
    }
}

class ChatMember extends MmbBase{
    /**
     * User info
     * اطلاعات کاربر
     *
     * @var user
     */
    public $user;
    /**
     * User status
     * مقام کاربر
     *
     * @var string
     */
    public $status;
    public const STATUS_CREATOR = 'creator';
    public const STATUS_ADMIN = 'administrator';
    public const STATUS_MEMBER = 'member';
    public const STATUS_LEFT = 'left';
    public const STATUS_RESTRICTED = 'restricted';
    public const STATUS_KICKED = 'kicked';

    /**
     * User title
     * لقب کاربر
     *
     * @var string
     */
    public $title;
    /**
     * Until date
     *
     * @var int
     */
    public $untilDate;
    /**
     * Is join?
     * عضویت کاربر
     *
     * @var bool
     */
    public $isJoin;
    /**
     * Is admin?
     * ادمین بودن کاربر
     *
     * @var bool
     */
    public $isAdmin;
    /**
     * Is anonymous?
     * ناشناس بودن کاربر
     *
     * @var bool
     */
    public $isAnonymous;
    /**
     * Permissions for admins and restricted users
     * دسترسی ها، تنها برای ادمین ها و کاربران محدود شده موجود است
     *
     * @var ChatPer
     */
    public $per;
    /**
     * @var MMB
     */
    private $_base;
    function __construct($v, $base){
        $this->_base = $base;
        $this->user = new user($v['user'], $base);
        $s = $this->status = $v['status'];
        $this->title = @$v['custom_title'];
        $this->untilDate = @$v['until_date'];
        $this->isJoin = $s == "member" || $s == "creator" || $s == "administrator";
        $this->isAdmin = $s == "creator" || $s == "administrator";
        $this->isAnonymous = @$v['is_anonymous'];
        
        if($s == "creator"){
            $this->per = new ChatPer('*', $this->isAnonymous, $base);
        }
        elseif($s == 'restricted'){
            $this->per = new ChatPer($v, $this->isAnonymous, $base);
        }
    }
}

class ChatPer extends MmbBase implements JsonSerializable{
    /**
     * Member or admin permission
     * @var bool
     */
    public $sendMsg;
    /**
     * Member or admin permission
     * @var bool
     */
    public $sendMedia;
    /**
     * Member or admin permission
     * @var bool
     */
    public $sendPoll;
    /**
     * Member or admin permission
     * @var bool
     */
    public $sendOther;
    /**
     * Member or admin permission
     * @var bool
     */
    public $webPre;
    /**
     * Member or admin permission
     * @var bool
     */
    public $changeInfo;
    /**
     * Member or admin permission
     * @var bool
     */
    public $invite;
    /**
     * Member or admin permission
     * @var bool
     */
    public $pin;

    /**
     * Admin permission
     *
     * @var bool
     */
    public $manageChat;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $delete;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $manageVoiceChat;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $restrict;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $promote;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $post;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $editPost;
    /**
     * Admin permission
     *
     * @var bool
     */
    public $isAnonymous;

    public function __construct($a, $isAnonymous, $base)
    {
        if($a == '*'){
            $this->sendMsg = true;
            $this->sendMedia = true;
            $this->sendPoll = true;
            $this->sendOther = true;
            $this->webPre = true;
            $this->changeInfo = true;
            $this->invite = true;
            $this->pin = true;

            $this->manageChat = true;
            $this->delete = true;
            $this->manageVoiceChat = true;
            $this->restrict = true;
            $this->promote = true;
            $this->post = true;
            $this->editPost = true;
        }
        else{
            $this->sendMsg = $a['can_send_messages'] ?? null;
            $this->sendMedia = $a['can_send_media_messages'] ?? null;
            $this->sendPoll = $a['can_send_polls'] ?? null;
            $this->sendOther = $a['can_send_other_messages'] ?? null;
            $this->webPre = $a['can_add_web_page_previews'] ?? null;
            $this->changeInfo = $a['can_change_info'] ?? null;
            $this->invite = $a['can_invite_users'] ?? null;
            $this->pin = $a['can_pin_messages'] ?? null;

            $this->manageChat = $a['can_manage_chat'] ?? null;
            $this->delete = $a['can_delete_messages'] ?? null;
            $this->manageVoiceChat = $a['can_manage_voice_chats'] ?? null;
            $this->restrict = $a['can_restrict_members'] ?? null;
            $this->promote = $a['can_promote_members'] ?? null;
            $this->post = $a['can_post_messages'] ?? null;
            $this->editPost = $a['can_edit_messages'] ?? null;
        }
        $this->isAnonymous = $isAnonymous;
    }

    /**
     * تبدیل شی به آرایه
     *
     * @return array
     */
    public function toArray(){
        $list = [
            'sendMsg',
            'sendMedia',
            'sendPoll',
            'sendOther',
            'webPre',
            'changeInfo',
            'invite',
            'pin',
            'manageChat',
            'delete',
            'manageVoiceChat',
            'restrict',
            'promote',
            'post',
            'editPost',
        ];
        $ar = [];
        foreach($list as $i){
            $value = $this->$i;
            if($value !== null){
                $ar[$i] = $value;
            }
        }
        return $ar;
    }

    public function jsonSerialize()
    {
        return $this->toArray();
    }

}
<?php

// Copyright (C): t.me/MMBlib

abstract class MmbDbBase extends MmbBase{

    public $__listeners = [];

    protected function installationConfig($dataPath, $tag = null){
        $ext = pathinfo($dataPath, PATHINFO_EXTENSION);
        if($ext == 'json'){
            $data = json_decode(file_get_contents($dataPath), true);
            if($tag !== false) $data = $data[$tag];
        }
        elseif($ext == 'php'){
            $_php = file_get_contents($dataPath);
            $_php = explode("\n", preg_match('/\/\*+\s*'.$tag.'([\s\S]*?)\*\//i', $_php, $_php) ? $_php[1] : "");
            $data = [];
            $_table = "";
            foreach($_php as $line){
                $line = explode('//', $line)[0];
                if(preg_match('/([a-zA-Z0-9_\.]+):/', $line, $_)){
                    $_table = $_[1];
                    $data[$_table] = [];
                }
                elseif(preg_match('/([a-zA-Z0-9_\.]+)[\s\t]+([a-zA-Z0-9_\.]+(\*|))/', $line, $_)){
                    $data[$_table][$_[1]] = $_[2];
                }
            }
        }
        else{
            return "Unsupported dataPath type";
        }

        Listeners::__runDbInstallation($this, $data);
        return $data;
    }

}

abstract class MmbDbBase_Sql extends MmbDbBase {

    /**
     * رشته ای از آخرین خطایی که رخ داده است
     *
     * @var string
     */
    public $error;

    /**
     * درخواست | ورودی ها را در درخواست، با علامت سوال ? بگذارید تا بصورت امن جایگزین شوند
     *
     * @param string $query
     * @param mixed ...$args
     * @return mixed
     */
    public function query($query, ...$args) {
        return $this->__query(preg_replace_callback('/`\?`|\?/', function($x) use($args){
            static $i = 0;
            if(isset($args[$i])){
                $arg = $args[$i++];
                if($x[0] == '?'){
                    if($arg === true)
                        return 1;
                    if($arg === false)
                        return 0;
                    return $this->secureString($arg);
                }
                else{
                    return "`$arg`";
                }
            }
            else{
                return $x[0];
            }
        }, $query));
    }

    public abstract function secureString($string);

    /**
     * ارسال درخواست بصورت مستقیم
     *
     * @param string $query
     * @return mixed
     */
    public abstract function __query($query);

    /**
     * تبدیل ردیف نتیجه به آرایه کلید دار
     *
     * @param mixed $result
     * @return array|false
     */
    public abstract function fetchAssoc($result);

    /**
     * تبدیل ردیف نتیجه به آرایه
     *
     * @param mixed $result
     * @return array|false
     */
    public abstract function fetchArray($result);

    /**
     * گرفتن تعداد ردیف نتیجه
     *
     * @param mixed $result
     * @return int
     */
    public abstract function numRows($result);


    /**
     * ارسال درخواست انتخاب و برگرداندن تعداد
     *
     * @param string $query
     * @param mixed ...$args
     * @return int
     */
    public function queryCount(string $query, ...$args) : int
    {
        $q = $this->query($query, ...$args);
        if($q) return $this->numRows($q);
        else return 0;
    }

    /**
     * ارسال درخواست و برگرداندن یک ردیف
     *
     * @param string $query
     * @param mixed ...$args
     * @return array|false
     */
    public function queryRow(string $query, ...$args)
    {
        $q = $this->query($query, ...$args);
        if($q) return $this->fetchAssoc($q);
        else return false;
    }

    /**
     * ارسال درخواست و برگرداندن یک خانه
     *
     * @param string $query
     * @param mixed ...$args
     * @return mixed
     */
    public function queryFirst(string $query, ...$args)
    {
        $q = $this->query($query, ...$args);
        if($q) return @$this->fetchArray($q)[0];
    }

    /**
     * درخواست | ورودی ها را در درخواست، با علامت سوال ? بگذارید تا بصورت امن جایگزین شوند
     *
     * @param string $query
     * @param mixed ...$args
     * @return Generator
     */
    public function queryEach($query, ...$args)
    {
        $q = $this->query($query, ...$args);
        if($q)
            while($row = $this->fetchAssoc($q))
                yield $row;
    }

    /**
     * درخواست | ورودی ها را در درخواست، با علامت سوال ? بگذارید تا بصورت امن جایگزین شوند
     *
     * @param string $query
     * @param mixed ...$args
     * @return array|false
     */
    public function queryAll($query, ...$args)
    {
        $q = $this->query($query, ...$args);
        $all = [];
        if(!$q)
            return false;
        else
            while($row = $this->fetchAssoc($q))
                $all[] = $row;
        return $all;
    }

    /**
     * Get where query with array
     * گرفتن کد بعد از where ایمن با آرایه
     * 
     * Example:
     * مثال:
     * $query = "SELECT * FROM `users` where " . $mmbMySql->getWhere(['name' => "Mahdi"]);
     * $res = $mmbMySql->query($query); // mixed value(isn't array)
     *
     * @param array $where
     * @param string $oper
     * @return string
     */
    function getWhere($where, $oper = "AND"){
        if(gettype($where) == "string"){
            return $where;
        }else{
            $r = "";
            foreach($where as $a => $b){
                $r .= $r==""?"":" $oper ";
                $r .= "`".$a."`='".addslashes($b)."'";
            }
            return $r;
        }
    }

    /**
     * Select and return first result(or false)
     * انتخاب و برگرداندن اولین نتیجه(یا false)
     *
     * @param string $table
     * @param array|string|false $where
     * @param string ...$args
     * @return array|false
     */
    function selectOnce($table, $where = false, ...$args){
        $q = "SELECT * FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        $q = $this->query("$q LIMIT 1", ...$args);
        if($q)
            return $this->fetchAssoc($q);
        else
            return false;
    }

    /**
     * Select and return all results
     * انتخاب و برگرداندن تمامی نتیجه ها
     *
     * @param string $table
     * @param array|string|false $where
     * @param string ...$args
     * @return array
     */
    function selectAll($table, $where = false, ...$args){
        $q = "SELECT * FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        $q = $this->query($q, ...$args);
        $r = [];
        if(!$q) return $r;
        while($row = $this->fetchAssoc($q)) {
            $r[] = $row;
        }
        return $r;
    }
    
    /**
     * Select each result rows and return as iterable
     * انتخاب تک تک سطر های نتیجه ها و برگردادن بصورت iterable
     * 
     * Example: مثال:
     * foreach($db->selectEach("users") as $user){
     *  if($user['id'] == 123456){
     *   echo "Found: ";
     *   print_r($user);
     *   break;
     *  }
     * }
     *
     * @param string $table
     * @param array|string|false $where
     * @param string ...$args
     * @return Generator
     */
    function selectEach($table, $where = false, ...$args){
        $q = "SELECT * FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        $q = $this->query($q, ...$args);
        if(!$q) return;
        while($row = $this->fetchAssoc($q)) {
            yield $row;
        }
    }
    
    /**
     * Select and return $max results
     * انتخاب و برگرداندن $max نتیجه
     *
     * @param string $table
     * @param int $max
     * @param array|string|false $where
     * @param string ...$args
     * @return array
     */
    function select2($table, $max, $where = false, ...$args){
        $q = "SELECT * FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        $q = $this->query("$q LIMIT $max", ...$args);
        $r = [];
        if(!$q) return $r;
        while($row = $this->fetchAssoc($q)) {
            $r[] = $row;
        }
        return $r;
    }
    
    /**
     * Select and return results num
     * انتخاب و برگرداندن تعداد نتیجه ها
     *
     * @param string $table
     * @param array|string|false $where
     * @param string ...$args
     * @return int
     */
    function selectNum($table, $where = false, ...$args){
        $q = "SELECT count(*) FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        $q = $this->query($q, ...$args);
        if($q)
            return @$this->fetchArray($q)[0];
        else
            return 0;
    }

    /**
     * Insert row in table
     * افزودن یک ردیف به تیبل
     *
     * @param string $table
     * @param array $keyVals
     * @return bool
     */
    public function insert(string $table, array $keyVals)
    {
        $keys = '';
        $vals = '';
        $first = true;
        foreach($keyVals as $key => $val){
            if($first){
                $first = false;
            }
            else{
                $keys .= ', ';
                $vals .= ', ';
            }
            $keys .= "`$key`";
            if($val === true)
                $val = 1;
            elseif($val === false)
                $val = 0;
            else
                $val = $this->secureString($val);
            $vals .= $val;
        }
        return $this->query("INSERT INTO `$table` ($keys) VALUES ($vals)");
    }

    /**
     * Update the row(s) values
     * بروزرسانی(ویرایش) سطر در تیبل
     *
     * @param string $table
     * @param array|string $values
     * @param array|string|false $where
     * @param string ...$args
     * @return void
     */
    function update($table, $values, $where = false, ...$args){
        if($where && is_array($where)){
            $where = $this->getWhere($where, $args[0] ?? "AND");
            $args = [];
            if(!is_array($values)){
                mmb_error_throw(static::class . ': `update` function with array $where argument, need array $values argument too', true);
            }
        }
        if(is_array($values)){
            $set = "";
            $first = true;
            foreach($values as $key => $val){
                if($first){
                    $first = false;
                }
                else{
                    $set .= ', ';
                }
                $set .= "`$key` = ";
                if($val === true)
                    $val = 1;
                elseif($val === false)
                    $val = 0;
                else
                    $val = $this->secureString($val);
                $set .= $val;
            }
        }
        else{
            $set = $values;
        }
        return $this->query("UPDATE `$table` SET $set" . ($where ? " WHERE $where" : ""), ...$args);
    }

    /**
     * Delete row(s)
     * حذف سطر(ها)
     *
     * @param string $table
     * @param array|string|false $where
     * @param string ...$args
     * @return void
     */
    function delete($table, $where = false, ...$args){
        $q = "DELETE FROM `$table`";
        if($where){
            if(is_array($where)){
                $q .= " WHERE ".$this->getWhere($where, $args[0] ?? "AND");
                $args = [];
            }
            else{
                $q .= " WHERE " . $where;
            }
        }
        return $this->query($q, ...$args);
    }

    /**
     * مقایسه دو تایپ اسکیوال
     *
     * @param string $typeA
     * @param string $typeB
     * @return bool
     */
    public static function compareTypes($typeA, $typeB){
        $typeB_ = $typeB;
        $nameA = $typeA;
        $nameB = $typeB;
        if(!strpos($typeA, "(")){
            $typeB = explode("(", $typeB)[0];
            $nameB = $typeB;
        }
        else{
            $nameA = explode("(", $typeA)[0];
            $nameB = explode("(", $typeB)[0];
        }
        $nameA = strtolower($nameA);
        $nameB = strtolower($nameB);
        switch($nameA){
            case 'bool': case 'boolean':
                return self::compareTypes('tinyint(1)', $typeB_);
        }
        if($nameA != $nameB){
            return false;
        }
        if(strtolower($typeA) != strtolower($typeB)){
            return false;
        }
        return true;
    }

    /**
     * بررسی وجود جدول
     *
     * @param string $table
     * @return bool
     */
    public abstract function existsTable($table);

    /**
     * گرفتن اطلاعات ستون های جدول
     *
     * @param string $table
     * @return MmbSqlCol[]
     */
    public abstract function getTableCols($table);

    /**
     * حذف ستون
     *
     * @param string $table
     * @param string $col
     * @return bool
     */
    public abstract function dropCol(string $table, string $col);

    /**
     * ایجاد جدول جدید
     *
     * @param string $table
     * @param MmbSqlCol[] $cols
     * @return bool
     */
    public abstract function createTable(string $table, $cols);

    /**
     * ویرایش ستون
     *
     * @param string $table
     * @param string $col
     * @param MmbSqlCol $newCol
     * @return bool
     */
    public abstract function changeCol(string $table, string $col, MmbSqlCol $newCol);

    /**
     * افزودن ستون جدید
     *
     * * `After`: برای ابتدا 0 و برای انتها -1 و برای بعد از ستون دلخواه، نام ستون را بدهید
     * 
     * @param string $table
     * @param MmbSqlCol $newCol
     * @param string $after
     * @return bool
     */
    public abstract function insertCol(string $table, MmbSqlCol $newCol, $after = -1);

    /**
     * Installation
     * راه اندازی ستون ها و داده های دییتابیس
     *
     * @param string $dataPath
     * @param string|false $tag
     * @return string
     */
    public function installation(string $dataPath, $tag = false){
        $data = $this->installationConfig($dataPath, $tag);
        if(is_string($data)) return $data;
                
        $status = [
            'table' => 0,
            'add' => 0,
            'remove' => 0,
            'edit' => 0,
            'fails' => 0
        ];

        foreach($data as $table => $cols){
            if($this->existsTable($table)){
                $cols_i = array_keys($cols);
                $b4cols = $this->getTableCols($table);
                foreach($b4cols as $info){
                    $col = $info->name;
                    if(!isset($cols[$col])){
                        // Delete col
                        if($this->dropCol($table, $col))
                            $status['remove']++;
                        else
                            $status['fails']++;
                        continue;
                    }
                    $type = $cols[$col];
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }
                    if(!self::compareTypes($type, $info->type) || $info->primary != $primary){
                        // Change type
                        if($this->changeCol($table, $col, new MmbSqlCol($col, $type, !$primary, $primary)))
                            $status['edit']++;
                        else
                            $status['fails']++;
                    }
                    unset($cols[$col]);
                }
                foreach($cols as $col => $type){
                    // Add col
                    $after = array_search($col, $cols_i);
                    if($after != 0) $after = str_replace("*", "", $cols_i[$after - 1]);
                    
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }
                    
                    if($this->insertCol($table, new MmbSqlCol($col, $type, !$primary, $primary), $after))
                        $status['add']++;
                    else
                        $status['fails']++;
                }
            }
            else{
                // Create table
                $colss = [];
                foreach($cols as $col => $type){
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }
                    $colss[] = new MmbSqlCol($col, $type, !$primary, $primary);
                }
                if($this->createTable($table, $colss))
                    $status['table']++;
                else
                    $status['fails']++;
            }
        }

        return (
            ($status['table'] ? "$status[table] new table(s) created!\n" : "") .
            ($status['add'] ? "$status[add] new column(s) added!\n" : "") .
            ($status['remove'] ? "$status[remove] column(s) removed!\n" : "") .
            ($status['edit'] ? "$status[edit] column(s) edited!\n" : "") .
            ($status['fails'] ? "$status[fails] try(s) failed!\n" : "")
        ) ?: "No changes";
    }

}

class MmbMySql extends MmbDbBase_Sql {
    private $_ = null;
    /**
     * Create mmbmysql object and connect to mysql database
     * ساخت شئ mmbmysql و اتصال به دیتابیس مای اس کیو ال
     *
     * @param string $host Host address (Localhost: '127.0.0.1') آدرس سرور حاوی دیتابیس
     * @param string $username Username | نام کاربری
     * @param string $password Password | رمز عبور
     * @param string $dbname Database name | نام دیتابیس
     */
    public function __construct(string $host, string $username, string $password, string $dbname){
        $this->_ = mysqli_connect($host, $username, $password, $dbname);
        if(!$this->_) {
            mmb_error_throw("MmbMySql: Connection failed");
        }
    }

    public function secureString($string)
    {
        return '"' . addslashes($string) . '"';
    }

    /**
     * گرفتن کلاس اصلی mysqli
     *
     * @return mysqli
     */
    public function getReal(){
        return $this->_;
    }

    /**
     * گرفتن آیدی اینسرت شده
     *
     * @return int
     */
    public function insertID(){
        return $this->_->insert_id;
    }

    public function __query($query)
    {
        $res = mysqli_query($this->_, $query);
        if($error = mysqli_error($this->_))
            $this->error = $error;
        return $res;
    }

    public function fetchArray($result)
    {
        return mysqli_fetch_row($result);
    }

    public function fetchAssoc($result)
    {
        return mysqli_fetch_assoc($result);
    }

    public function numRows($result)
    {
        return mysqli_num_rows($result);
    }

    public function existsTable($table)
    {
        return $this->query("DESCRIBE `?`", $table);
    }

    public function createTable(string $table, $cols)
    {
        $colss = [];
        foreach($cols as $col){
            $q = "`{$col->name}` {$col->type}";
            if($col->primary){
                $q .= " AUTO_INCREMENT PRIMARY KEY";
            }
            else{
                $q .= " NOT NULL";
            }
            $colss[] = $q;
        }
        return $this->query('CREATE TABLE `?` (
            ' . join($colss, " ,\n") . '
        )', $table);
    }

    public function getTableCols($table)
    {
        $cols = $this->query("SHOW COLUMNS FROM `?`", $table);
        $r = [];
        while($col = $this->fetchAssoc($cols)) {
            $r[] = new MmbSqlCol($col['Field'], $col['Type'], $col['Null'] == "NO", $col['Extra'] == "auto_increment");
        }
        return $r;
    }

    public function insertCol(string $table, MmbSqlCol $newCol, $after = -1)
    {
        $at = '';
        if($after === 0) $at = "FIRST";
        elseif($after === -1) $at = "";
        else $at = "AFTER `$after`";

        if($newCol->primary){
            $opt = "AUTO_INCREMENT PRIMARY KEY";
        }
        else{
            $opt = "NOT NULL";
        }
        
        return $this->query("ALTER TABLE `$table` ADD `{$newCol->name}` {$newCol->type} $opt $at");
    }

    public function changeCol(string $table, string $col, MmbSqlCol $newCol)
    {
        if($newCol->primary){
            $opt = "AUTO_INCREMENT PRIMARY KEY";
        }
        else{
            $opt = "NOT NULL";
        }
        return $this->query("ALTER TABLE `$table` CHANGE `$col` `{$newCol->name}` {$newCol->type} $opt");
    }

    public function dropCol(string $table, string $col)
    {
        return $this->query("ALTER TABLE `$table` DROP `$col`");
    }

    /**
     * Installation
     * راه اندازی ستون ها و داده های دییتابیس
     *
     * @param string $dataPath
     * @param boolean $tag
     * @return void
     *//*
    public function installation(string $dataPath, $tag = false){
        $data = $this->installationConfig($dataPath, $tag);
        if(is_string($data)) return $data;
                
        function compareTypes($typeA, $typeB){
            $typeB_ = $typeB;
            $nameA = $typeA;
            $nameB = $typeB;
            if(!strpos($typeA, "(")){
                $typeB = explode("(", $typeB)[0];
                $nameB = $typeB;
            }
            else{
                $nameA = explode("(", $typeA)[0];
                $nameB = explode("(", $typeB)[0];
            }
            $nameA = strtolower($nameA);
            $nameB = strtolower($nameB);
            switch($nameA){
                case 'bool': case 'boolean':
                    return compareTypes('tinyint(1)', $typeB_);
            }
            if($nameA != $nameB){
                return false;
            }
            if(strtolower($typeA) != strtolower($typeB)){
                return false;
            }
            return true;
        }

        $status = [
            'table' => 0,
            'add' => 0,
            'remove' => 0,
            'edit' => 0,
            'fails' => 0
        ];

        foreach($data as $table => $cols){
            if($this->query("DESCRIBE `?`", $table)){
                $cols_i = array_keys($cols);
                $b4cols = $this->query("SHOW COLUMNS FROM `?`", $table);
                while($info = $b4cols->fetch_assoc()){
                    $col = $info['Field'];
                    if(!isset($cols[$col])){
                        // Delete col
                        if($this->query("ALTER TABLE `$table` DROP `$col`"))
                            $status['remove']++;
                        else
                            $status['fails']++;
                        continue;
                    }
                    $type = $cols[$col];
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }
                    if(!compareTypes($type, $info['Type']) || ($info['Extra']=="auto_increment") != $primary){
                        // Change type
                        if($primary){
                            $opt = "AUTO_INCREMENT PRIMARY KEY";
                        }
                        else{
                            $opt = "NOT NULL";
                        }
                        if($this->query("ALTER TABLE `$table` CHANGE `$col` `$col` $type $opt"))
                            $status['edit']++;
                        else
                            $status['fails']++;
                    }
                    unset($cols[$col]);
                }
                foreach($cols as $col => $type){
                    // Add col
                    $at = array_search($col, $cols_i);

                    if($at == 0) $at = "FIRST";
                    else $at = "AFTER " . str_replace("*", "", $cols_i[$at - 1]);
                    
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }

                    if($primary){
                        $opt = "AUTO_INCREMENT PRIMARY KEY";
                    }
                    else{
                        $opt = "NOT NULL";
                    }
                    
                    if($this->query("ALTER TABLE `$table` ADD `$col` $type $opt $at"))
                        $status['add']++;
                    else
                        $status['fails']++;
                }
            }
            else{
                $colss = [];
                foreach($cols as $col => $type){
                    $primary = false;
                    if(strpos($type, "*")){
                        $primary = true;
                        $type = str_replace("*", "", $type);
                    }
                    $q = "`$col` $type";
                    if($primary){
                        $q .= " AUTO_INCREMENT PRIMARY KEY";
                    }
                    else{
                        $q .= " NOT NULL";
                    }
                    $colss[] = $q;
                }
                if($this->query('CREATE TABLE `?` (
                    ' . join($colss, " ,\n") . '
                )', $table))
                    $status['table']++;
                else
                    $status['fails']++;
            }
        }

        return (
            ($status['table'] ? "$status[table] new table(s) created!\n" : "") .
            ($status['add'] ? "$status[add] new column(s) added!\n" : "") .
            ($status['remove'] ? "$status[remove] column(s) removed!\n" : "") .
            ($status['edit'] ? "$status[edit] column(s) edited!\n" : "") .
            ($status['fails'] ? "$status[fails] try(s) failed!\n" : "")
        ) ?: "No changes";
    }*/
}

class MmbSqlite extends MmbDbBase_Sql {

    /**
     * @var Sqlite3
     */
    private $db = null;
    /**
     * Create mmbmysql object and connect to mysql database
     * ساخت شئ mmbmysql و اتصال به دیتابیس مای اس کیو ال
     *
     * @param string $filename
     * @param string $key
     */
    public function __construct(string $filename, string $key = null){
        if(!class_exists('SQLite3')) {
            mmb_error_throw("MmbSqlite: Sqlite is disabled in server settings! Turn it on and try again");
        }
        $this->db = new SQLite3($filename, SQLITE3_OPEN_READWRITE | SQLITE3_OPEN_CREATE, $key);
        if(!$this->db) {
            mmb_error_throw("MmbSqlite: Connection failed");
        }
    }

    public function secureString($string)
    {
        return "'" . str_replace("'", "''", $string) . "'";
    }

    public function __query($query)
    {
        $res = $this->db->query($query);
        if($error = $this->db->lastErrorMsg())
            $this->error = $error;
        return $res;
    }

    public function fetchArray($result)
    {
        return $result->fetchArray(SQLITE3_NUM);
    }

    public function fetchAssoc($result)
    {
        return $result->fetchArray(SQLITE3_ASSOC);
    }

    public function numRows($result)
    {
        $result->reset();
        $num = 0;
        while($result->fetchArray(SQLITE3_NUM)) {
            $num++;
        }
        return $num;
    }

    public function existsTable($table)
    {
        return @$this->query("SELECT * FROM `?` LIMIT 0", $table) ? true : false;
    }

    public function createTable(string $table, $cols)
    {
        $colss = [];
        foreach($cols as $col){
            $q = "`{$col->name}` {$col->type}";
            if($col->primary){
                $q .= " PRIMARY KEY AUTOINCREMENT";
            }
            elseif($col->not_null){
                $q .= " NOT NULL";
            }
            $colss[] = $q;
        }
        return $this->query('CREATE TABLE `?` (
            ' . join($colss, " ,\n") . '
        )', $table) ? true : false;
    }

    public function getTableCols($table)
    {
        $cols = $this->query("PRAGMA table_info(`$table`)", $table);
        $r = [];
        while($col = $this->fetchAssoc($cols)) {
            $r[] = new MmbSqlCol($col['name'], $col['type'], $col['notnull'] == 1, $col['pk'] == 1);
        }
        return $r;
    }

    public function insertCol(string $table, MmbSqlCol $newCol, $after = -1)
    {
        $at = '';
        /*if($after === 0) $at = "FIRST";
        elseif($after === -1) $at = "";
        else $at = "AFTER `$after`";*/

        if($newCol->primary){
            $opt = "AUTO_INCREMENT PRIMARY KEY";
        }
        else{
            $opt = ""; // "NOT NULL"; Can't add not null column...
        }
        
        return $this->query("ALTER TABLE `$table` ADD `{$newCol->name}` {$newCol->type} $opt $at") ? true : false;
    }

    public function changeCol(string $table, string $col, MmbSqlCol $newCol)
    {
        $newCol->not_null = false;
        $cols = $this->getTableCols($table);
        $cols0 = [];
        $cols1 = [];
        $colsCr = [];
        foreach($cols as $a) {
            $b = $a;
            if($b->name == $col) $b = $newCol;
            $colsCr[] = $b;

            if($a->name == $col) {
                $cols0[] = '`' . $a->name . '`';
                $cols1[] = '`' . $newCol->name . '`';
            }
            else {
                $cols0[] = '`' . $a->name . '`';
                $cols1[] = '`' . $a->name . '`';
            }
        }
        $temp = 'temp_table_' . $table;
        $this->createTable($temp, $colsCr);
        $this->query("INSERT INTO `$temp` (" . join($cols1, ", ") . ") SELECT " . join($cols0, ", ") . " FROM `$table`");

        if($this->selectNum($table) != $this->selectNum($temp)) {
            $this->query("DROP TABLE `$temp`");
            return false;
        }
        $this->query("DROP TABLE `$table`");
        $this->query("ALTER TABLE `$temp` RENAME TO `$table`");

        return true;
    }

    public function dropCol(string $table, string $col)
    {
        $cols = $this->getTableCols($table);
        $cols0 = [];
        $cols1 = [];
        $colsCr = [];
        foreach($cols as $a) {
            if($a->name != $col) $colsCr[] = $a;

            if($a->name != $col) {
                $cols0[] = '`' . $a->name . '`';
                $cols1[] = '`' . $a->name . '`';
            }
        }
        $temp = 'temp_table_' . $table;
        $this->createTable($temp, $colsCr);
        $this->query("INSERT INTO `$temp` (" . join($cols1, ", ") . ") SELECT " . join($cols0, ", ") . " FROM `$table`");

        if($this->selectNum($table) != $this->selectNum($temp)) {
            $this->query("DROP TABLE `$temp`");
            return false;
        }
        $this->query("DROP TABLE `$table`");
        $this->query("ALTER TABLE `$temp` RENAME TO `$table`");

        return true;
    }

}

class MmbSqlCol {

    /**
     * نام ستون
     *
     * @var string
     */
    public $name;

    /**
     * نوع ستون
     *
     * @var string
     */
    public $type;

    /**
     * نال نبودن
     *
     * @var bool
     */
    public $not_null;

    /**
     * پر شدن خودکار
     *
     * @var bool
     */
    public $primary;

    public function __construct($name, $type, $not_null = true, $primary = false) {
        $this->name = $name;
        $this->type = $type;
        $this->not_null = $not_null;
        $this->primary = $primary;
    }

}


abstract class MmbDbBase_NoSql extends MmbDbBase {

}

class MmbJson extends MmbDbBase_NoSql{
    private $_;

    /**
     * Create new mmbjson object
     * ساخت یک شئ mmbjson
     *
     * @param string $dir Data directory | محل دیتا
     */
    public function __construct($dir){
        if(!file_exists($dir))
            mkdir($dir);
        $this->_ = $dir;
    }
    
    /**
     * Get number of rows in table
     * گرفتن تعداد سطر ها در تیبل
     *
     * @param string $table
     * @return int
     */
    public function number($table){
        return count(glob($this->_ . "/" . $table . "/*.json"));
    }

    /**
     * گرفتن مسیر فایل دیتا
     *
     * @param string $table
     * @param string $name
     * @return string
     */
    public function getPath($table, $name) {
        return $this->_."/$table/$name.json";
    }
    
    /**
     * Select and return row value
     * انتخاب و گرفتن مقدار یک ردیف
     *
     * @param string $table
     * @param string $name
     * @return mixed|false
     */
    public function select($table, $name){
        return file_exists($this->_."/$table/$name.json") ?
            json_decode(Files::get($this->_."/$table/$name.json"), true) :
            false;
    }
    
    /**
     * Select all and return assoc array
     * انتخاب تمامی ردیف ها و برگرداندن آنها به صورت آرایه کلید دار
     *
     * @param string $table
     * @return array
     */
    public function selectAll($table){
        $r = [];
        $n = strlen($this->_) + strlen($table) + 2;
        foreach(glob($this->_."/$table/*.json") as $name_dir){
            $r[substr($name_dir, $n, strlen($name_dir) - $n - 4)] = json_decode(Files::get($name_dir), true);
        }
        return $r;
    }
    
    /**
     * Select and return all names
     * انتخاب و برگرداندن تمامی نام های ردیف ها
     *
     * @param string $table
     * @return array
     */
    public function selectNames($table){
        $r = [];
        $l = strlen($this->_) + strlen($table) + 2;
        foreach(glob($this->_."/$table/*.json") as $name_dir){
            $r[] = substr($name_dir, $l, strlen($name_dir) - $l - 5);
        }
        return $r;
    }
    
    /**
     * Select each name and return iterable object
     * انتخاب تمامی نام های ردیف ها و برگرداندن به صورت iterable
     * 
     * Example:
     * مثال:
     * foreach($mmbJson->selectEachNames("users") as $name){
     *   echo $name . PHP_EOL;
     * }
     *
     * @param string $table
     * @return 
     */
    public function selectEachNames($table){
        $l = strlen($this->_) + strlen($table) + 2;
        foreach(glob($this->_."/$table/*.json") as $name_dir){
            yield substr($name_dir, $l, strlen($name_dir) - $l - 5);
        }
    }
    
    /**
     * Insert(or replace) data in row with name
     * افزودن(یا جایگزینی) دیتای ردیف با اسم آن
     *
     * @param string $table
     * @param string $name
     * @param mixed $data
     * @return bool
     */
    public function insert($table, $name, $data){
        return Files::put($this->_."/$table/$name.json", json_encode($data)) !== false;
    }
    
    /**
     * Replace(or insert) data in row with name
     * جایگزینی(یا افزودن) دیتای ردیف با اسم آن
     *
     * @param string $table
     * @param string $name
     * @param mixed $newData
     * @return bool
     */
    public function replace($table, $name, $newData){
        return $this->insert($table, $name, $newData);
    }
    
    /**
     * Delete row
     * حذف ردیف
     *
     * @param string $table
     * @param string $name
     * @return bool
     */
    public function delete($table, $name){
        return @unlink($this->_."/$table/$name.json");
    }
    
    /**
     * بروزرسانی کلید های ردیف(باید مقدار ردیف آرایه باشد)
     * 
     * مثال:
     * * `// Data, before updating: ['score' => 0, 'step' => "none"]`
     * * `$mmbJson->update("users", $id, ['step' => "working"]);`
     * * `// Data, after updating: ['score' => 0, 'step' => "working"]`
     *
     * @param string $table
     * @param string $name
     * @param array $editVals
     * @return bool
     */
    public function update($table, $name, $editVals){
        Files::editText($this->_."/$table/$name.json", function($data) use(&$editVals) {
            $data = json_decode($data, true);
            $data = array_replace($data, $editVals);
            return json_encode($data);
        });
    }
    
    /**
     * Create table
     * ساخت تیبل
     *
     * @param string $table
     * @return bool
     */
    public function createTable($table){
        return @mkdir($this->_ . "/$table");
    }

    /**
     * Create tables
     * ساخت چند تیبل
     *
     * @param array $tables
     * @return bool
     */
    public function createTables($tables){
        $successFull = true;
        foreach($tables as $table)
            if(!$this->createTable($table))
                $successFull = false;
        return $successFull;
    }

    /**
     * Select all rows(only values) and return iterable object
     * انتخاب تمامی ردیف ها(تنها مقدار آنها) و برگردادن iterable
     * 
     * Example:
     * مثال:
     * foreach($mmbJson->selectEach("users") as $val){
     *   var_dump($val);
     * }
     *
     * @param string $table
     * @return 
     */
    public function selectEach($table){
        foreach(glob($this->_."/$table/*.json") as $name_dir){
            yield json_decode(Files::get($name_dir), true);
        }
    }

    /**
     * Check exists table
     * بررسی وجود تیبل
     *
     * @param string $table
     * @return bool
     */
    public function existsTable($table){
        return file_exists($this->_."/$table/");
    }

    /**
     * Is database initialized
     * آیا دیتابیس مقدار دهی شده است
     *
     * @return boolean
     */
    public function isInit(){
        return count(glob($this->_)) ? true : false;
    }
    
    /**
     * Installation
     * راه اندازی ستون ها و داده های دییتابیس
     *
     * @param string $dataPath
     * @param boolean $tag
     * @return void
     */
    public function installation(string $dataPath, $tag = false){
        $data = $this->installationConfig($dataPath, $tag);
        if(is_string($data)) return $data;

        $status = [
            'table' => 0,
            'add' => 0,
            'remove' => 0,
            'edit' => 0,
            'fails' => 0
        ];

        foreach($data as $table => $cols){
            if($this->createTable($table))
                $status['table']++;
        }

        return (
            ($status['table'] ? "$status[table] new table(s) created!\n" : "") .
            ($status['add'] ? "$status[add] new column(s) added!\n" : "") .
            ($status['remove'] ? "$status[remove] column(s) removed!\n" : "") .
            ($status['edit'] ? "$status[edit] column(s) edited!\n" : "") .
            ($status['fails'] ? "$status[fails] try(s) failed!\n" : "")
        ) ?: "No changes";
    }
}


abstract class Table {

    private $old_data;
    public final function __construct(array $data)
    {
        $this->old_data = $data;
        foreach($data as $key => $value) {
            $this->$key = $value;
        }
    }

    public function getData() {
        $res = [];
        foreach($this->old_data as $key => $value) {
            $res[$key] = $this->$key;
        }
        return $res;
    }
    public function getChangedData() {
        $res = [];
        $data = $this->getData();
        foreach($this->old_data as $key => $value) {
            if($data[$key] !== $value)
                $res[$key] = $this->$key;
        }
        return $res;
    }

    /**
     * گرفتن نام این جدول
     *
     * @return string
     */
    public static function getName() {
        $name = static::class;
        if(endsWith($name, "Db", true))
            $name = substr($name, 0, -2);
        return $name;
    }

    /**
     * گرفتن نام ستون یکتا
     * در صورتی که ستون یکتا نداشته باشد، فالس را برمیگرداند
     *
     * @return string
     */
    public static function uniqueCol() {
        return 'id';
    }

    /**
     * گرفتن مقدار های پیشفرض ستون ها (برای زمان ایجاد ردیف جدید)
     *
     * @return array
     */
    public static function defaults() {
        return [];
    }

    /**
     * گرفتن یک ردیف
     *
     * @param mixed $id
     * @param string $find_by
     * @return static|false
     */
    public static function get($id, $find_by = null) {
        if($find_by === null)
            $find_by = static::uniqueCol();
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $res = $db->selectOnce(static::getName(), "`?` = ?", $find_by, $id);
        if($res) {
            return new static($res);
        }
        else {
            return false;
        }
    }

    /**
     * گرفتن تمامی ردیف ها
     *
     * @param mixed $id
     * @param string $find_by
     * @return static[]|false
     */
    public static function getAll() {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $res = $db->selectAll(static::getName());
        if($res) {
            foreach($res as $i => $row) {
                $res[$i] = new static($row);
            }
            return $res;
        }
        else {
            return false;
        }
    }

    /**
     * انتخاب یک ردیف
     *
     * @param array|string|false $where
     * @param mixed ...$args
     * @return static|false
     */
    public static function selectOnce($where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $res = $db->selectOnce(static::getName(), $where, ...$args);
        if($res) {
            return new static($res);
        }
        else {
            return false;
        }
    }

    /**
     * انتخاب چند ردیف
     *
     * @param array|string|false $where
     * @param mixed ...$args
     * @return static[]|false
     */
    public static function selectAll($where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $res = $db->selectAll(static::getName(), $where, ...$args);
        if($res) {
            foreach($res as $i => $row) {
                $res[$i] = new static($row);
            }
            return $res;
        }
        else {
            return false;
        }
    }

    /**
     * انتخاب چند ردیف مناسب حلقه
     *
     * @param array|string|false $where
     * @param mixed ...$args
     * @return Generator|static[]
     */
    public static function selectEach($where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $res = $db->selectEach(static::getName(), $where, ...$args);
        if($res) {
            foreach($res as $row) {
                yield new static($row);
            }
        }
    }

    /**
     * انتخاب تعداد
     *
     * @param array|string|false $where
     * @param mixed ...$args
     * @return int
     */
    public static function selectNum($where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        return $db->selectNum(static::getName(), $where, ...$args);
    }

    /**
     * حذف یک یا چند ردیف
     *
     * @param array|string|false $where
     * @param mixed ...$args
     * @return void
     */
    public static function deleteRows($where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        $db->delete(static::getName(), $where, ...$args);
    }

    /**
     * ساخت ردیف جدید
     *
     * @param array|static $values
     * @return static|false
     */
    public static function insert($values = []) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        if($values instanceof Table)
            $values = $values->getData();
        else
            $values = array_replace(static::defaults(), $values);
            
        if(!$db->insert(static::getName(), $values)) {
            return false;
        }
        if($uniq = static::uniqueCol()) {
            if($db instanceof MmbMySql)
                $values[$uniq] = $db->insertID();
        }
        return new static($values);
    }

    /**
     * ویرایش یک یا چند ردیف
     *
     * @param string|array|static $values
     * @param array|string|false $where
     * @param mixed ...$args
     * @return void
     */
    public static function update($values, $where = false, ...$args) {
        /** @var MmbDbBase_Sql */
        $db = Atom::$db;
        if(!($db instanceof MmbDbBase_Sql)) {
            mmb_error_throw("Required sql database");
        }
        if($values instanceof Table)
            $values = $values->getChangedData();
        return $db->update(static::getName(), $values, $where, ...$args);
    }



    /**
     * شروع کلاس
     *
     * @return void
     */
    public function init() {
    }

    /**
     * ذخیره ردیف
     *
     * @param boolean $onlyChanges آیا فقط تغییرات ذخیره شود؟
     * @return void
     */
    public function save($onlyChanges = true) {
        if($onlyChanges) {
            $values = $this->getChangedData();
            if(!$values) return;
        }
        else {
            $values = $this->getData();
        }
        $un = static::uniqueCol();
        if(!$un) {
            mmb_error_throw("Method '" . __METHOD__ . "' required uniqueCol in class '" . static::class . "'");
        }
        return static::update($values, "`?` = ?", $un, $this->$un);
    }

    /**
     * حذف این ردیف از جدول
     *
     * @return void
     */
    public function delete() {
        $un = static::uniqueCol();
        if(!$un) {
            mmb_error_throw("Method '" . __METHOD__ . "' required uniqueCol in class '" . static::class . "'");
        }
        return static::deleteRows("`?` = ?", $un, $this->$un);
    }

}
<?php

if(!MMB_DEVELOP) return;

if(
    php_sapi_name() == 'cli' &&
    isset($argv) && $argv// &&
    //MMB_FILE_PATH == realpath($argv[0])
) {
    Listeners::onReady(function() use(&$argv) {
        $cmd = $argv[1] ?? '';

        if($cmd == '-v' || $cmd == 'v' || $cmd == 'version' || $cmd == '-version' || $cmd == '--v' || $cmd == '--version') {
            echo "Mmb " . MMB_VERSION;
        }

        elseif($cmd == 'help' || $cmd == '-help' || $cmd == '--help') {
            echo "Mmb develop tool - help

    Help
        help : Show help
        version : Show version

    Plugins
        plugin-create [name] : Create plugin
        p-create [name] : Create plugin

    Source
        config [file] : Create basic config file
        new [file] ([config]) ([db-config]) : Create new mmb bot source
            ";
        }

        elseif($cmd == 'p-create' || $cmd == 'plugin-create') {
            $name = $argv[2];
            if(preg_match('/^[a-zA-Z0-9_]+$/', $name)) {
                $namelow = strtolower($name);
                $name = strtoupper($name[0]) . substr($name, 1);
                $path = PLUGINS . "/plugin.$namelow.php";
                if(file_exists($path)) {
                    echo "Error: plugin $name is exists";
                }
                else {
                    @mkdir(PLUGINS);
                    Files::put($path, "<?php
/** 
 * پلاگین دلخواه
 */
class Plugin$name extends Plugin {

    /**
     * متغیر عمومی
     *
     * @var string
     */
    public static \$helloWorld = 'Hello world';

    /**
     * شروع کار پلاگین
     *
     * @return void
     */
    public static function start()
    {
        // افزودن شنونده آپدیت
        Listeners::onUpd([static::class, 'newUpd'], true);
    }

    /**
     * دریافت آپدیت جدید
     *
     * @param Upd \$upd
     * @return bool
     */
    public static function newUpd(Upd \$upd) {
        if(\$msg = \$upd -> msg) {
            // پاسخ به پیام مورد نظر
            if(\$msg -> text == \"hello\") {
                \$msg -> replyText(static::\$helloWorld);
            }
        }
    }

}

// افزودن و معرفی پلاگین به ام ام بی
Plugins::addPlugin('Plugin$name');
                    ");
                    echo "Plugin '$name' created!";
                }
            }
            else {
                echo "Error: name is not valid!";
            }
        }

        elseif($cmd == 'config') {
            $file = $argv[2] ?? die("Error: File name is not set!");

            if(file_exists($file)) {
                echo "Error: File '$file' is exists!";
            }
            else {
                Files::put($file, "<?php

// فراخوانی ام ام بی
require_once __DIR__ . '/Mmb.php';
// require_once __DIR__ . '/db-config.php';


// تنظیمات اصلی
Atom::\$mmb = \$mmb = new Mmb('TOKEN');
Atom::\$db = \$db = new MmbMySql('localhost', 'username', 'password', 'dbname');
// Atom::\$dbConfig = __DIR__ . '/db-config.php';


// دیگر تنظیمات
Atom::\$botUsername = \$botUsername = \"USERNAME\";
Atom::\$dbNewUserIfMsg = true;
Atom::\$dbNewUserIfMsgPV = true;
// Atom::\$dbNewUser = function(\$id) {
//     return [
//         'col' => 'value'
//     ];
// };


// شروع
Atom::start();
                ");
                echo "Config created!";
            }
        }

        elseif($cmd == 'new') {
            $file = $argv[2] ?? die("Error: File name is not set!");
            $config = $argv[3] ?? '';
            $dbconfig = $argv[4] ?? '';
            $base = dirname(getAbsPath($file));

            if(file_exists($file)) {
                echo "Error: File '$file' is exists!";
                exit;
            }
            elseif($config && file_exists($config)) {
                echo "Error: File '$config' is exists!";
                exit;
            }
            elseif($dbconfig && file_exists($dbconfig)) {
                echo "Error: File '$dbconfig' is exists!";
                exit;
            }

            if($config) {
                $base2 = dirname(getAbsPath($config));
                @mkdir($base2);
                $cm = $dbconfig ? '' : '// ';
                $dbc = $dbconfig ? "/" . getRelPath($dbconfig, $base2) : "/db-config.php";
                Files::put($config, "<?php

// فراخوانی ام ام بی
require_once __DIR__ . '/Mmb.php';
{$cm}require_once __DIR__ . '$dbc';


// تنظیمات اصلی
Atom::\$mmb = \$mmb = new Mmb('TOKEN');
Atom::\$db = \$db = new MmbMySql('localhost', 'username', 'password', 'dbname');
{$cm}Atom::\$dbConfig = __DIR__ . '$dbc';


// دیگر تنظیمات
Atom::\$botUsername = \$botUsername = \"USERNAME\";
Atom::\$dbNewUserIfMsg = true;
Atom::\$dbNewUserIfMsgPV = true;
// Atom::\$dbNewUser = function(\$id) {
//     return [
//         'col' => 'value',
//         'test_int' => 0,
//         'test_text' => ''
//     ];
// };


// تنظیمات ادمین ها
Admins::\$developers = [123456789, 234567890]; // تمام دسترسی ها *
/* Admins::\$admins = [
    123123123 => [
        'canSeePanel' => true
    ]
]; */


// شروع
Atom::start();
                ");
            }
            if($dbconfig) {
                @mkdir(dirname(getAbsPath($dbconfig)));
                Files::put($dbconfig, "<?php

// Note: `users` table has been initialized in Atom with `id`, `step`, `data`, `map`, `join_time`, `invite_from`
// Note: `groups` table(if enabled) has been initialized in Atom with `id`, `data`

// Fill this comment
/** Database
 * 
 * users:
 * col          type
 * test_int     int
 * test_text    text
 * 
 * custom_table:
 * col1         text
 * col2         int
 * col3         text
 * 
 */



class UserRow extends UserRowBase{
    
    /**
     * ستون دلخواه
     *
     * @var type
     */
    public \$col;
    
    /**
     * ستون دلخواه
     *
     * @var int
     */
    public \$test_int;
    
    /**
     * ستون دلخواه
     *
     * @var string
     */
    public \$test_text;
    
    /**
     * مقدار دهی اولیه
     *
     * متغیر ها قبل از این بخش تعریف شده اند
     * 
     * @return void
     */
    public function init2()
    {
        if(\$this->test_text) {
            \$this->test_text = 'Default';
        }
    }
    
    /**
     * گرفتن اطلاعات جهت ذخیره
     * 
     * توجه: در صورت وارد نکردن، اطلاعات از متغیر ها گرفته می شوند
     *
     * @return array
     */
    public function getData2()
    {
        return [
            'col' => 'Edited'
        ];
    }

    public static function defaults() {
        return [
            'col' => 'value',
            'test_int' => 0,
            'test_text' => ''
        ];
    }
    
}


class GroupRow extends GroupRowBase{
    
    /**
     * مقدار دهی اولیه
     * 
     * متغیر ها قبل از این بخش تعریف شده اند
     *
     * @return void
     */
    public function init2()
    {
    }
    
    /**
     * گرفتن اطلاعات جهت ذخیره
     * 
     * توجه: در صورت وارد نکردن، اطلاعات از متغیر ها گرفته می شوند
     *
     * @return array
     */
    public function getData2()
    {
    }
    
    public static function defaults() {
        return [
        ];
    }
    
}


class AdminPer extends AdminPerBase{
    
    /**
     * تست متغیر دلخواه
     *
     * @var bool
     */
    public \$canSeePanel;
    
    /**
     * مقدار دهی اولیه
     *
     * @return void
     */
    public function init()
    {
        
    }
    
    /**
        * مقدار دهی اولیه با ورودی ویژه
        *
        * @param string \$as
        * @return void
        */
    public function initAs(\$as)
    {
        if(\$as == '*') {
            \$this->canSeePanel = true;
        }
    }
    
    /**
        * گرفتن اطلاعات جهت ذخیره
        * 
        * توجه: بهتر است تمامی متغیر ها را وارد کنید
        *
        * @return array
        */
    public function getData()
    {
        return [
            'canSeePanel' => \$this->canSeePanel
        ];
    }
    
}

                ");
            }
            
            @mkdir($base);
            $configText = $config ?
                "require_once __DIR__ . '/" . getRelPath($config, $base) . "';" :
                    trim2("
                    // فراخوانی ام ام بی
                    require_once __DIR__ . '/Mmb.php';
                    \t
                    // تنظیمات اصلی
                    Atom::\$mmb = \$mmb = new Mmb('TOKEN');
                    Atom::\$db = \$db = new MmbMySql('localhost', 'username', 'password', 'dbname');
                    ");
            Files::put($file, "<?php

$configText



if (\$upd = \$mmb->getUpd()) {
    if (\$msg = \$upd->msg) {
        \$text = \$msg->text;
        \$fromID = \$msg->from->id;
        
        \$user = UsersDb::getThis();
        \$admin = Admins::getAdmin(\$fromID);
        
        // نصب خودکار دیتابیس
        if(!\$user) {
            if(Atom::\$dbNewUser) {
                if(\$admin || Admins::countFull() == 0) {
                    \$res = Atom::installationDb();
                    replyText(\"عملیات نصب دیتابیس انجام شد، نتیجه:\\n\\n\$res\")
                        ->replyText(\"ربات را دوباره استارت کنید و در صورت بروز مشکل، از صحت تنظیمات خود اطمینان حاصل کنید\");
                }
                else {
                    replyText(\"ربات با مشکلی مواجه شده است! لطفا به ادمین گزارش دهید: #مشکل_نصب_دیتابیس\");
                }
                exit;
            }
            else {
                replyText(\"خطا: دیتابیس ربات نصب نیست یا مشکل دارد\");
                exit;
            }
        }
        
        
        
        if(\$msg -> started) {
            Start:
            \$msg->replyText(\"خوش آمدید!\", [
                'key' => [
                    [['text' => \"دکمه تست\"]]
                ]
            ]);
            \$user->step = 'start';
        }
        
        elseif(\$text == \"دکمه تست\" && \$user->step == 'start') {
            replyText(\"این دکمه یک دکمه تستی است!\");
        }
        
        // ذخیره ی تنها مقدار های جدید کاربر
        \$user->save();
        
    }
}
            ");

            echo "Successful! Enjoy :)";

        }

        else {
            return;
        }

        exit;
    });
}
<?php


class MmbException extends Exception{

}

class AtomRequiredException extends MmbException{
    
    public function __construct()
    {
        $this->message = "You should use 'Atom::start();' (Learn more: mmblib.ir)";
    }

}
<?php

// Copyright (C): t.me/MMBlib

/**
 * ساخت کیبورد
 *
 * @param array $key Keys
 * @param bool|null $inline is inline(null = auto)
 * @param boolean $resize Resize keyboard
 * @param boolean $encode Encode result with json
 * @param boolean $once One time keyboard
 * @param boolean $selective Selective
 * @return string|array
 */
function mkey($key, $inline=null, $resize=true, $encode=true, $once=false, $selective=false){
    if(isset($key['key'])){
        return mkey(
            $key['key'],
            $key['inline'] ?? null,
            $key['resize'] ?? true,
            $key['encode'] ?? true,
            $key['once'] ?? false,
            $key['selective'] ?? false
        );
    }
    if(($key = filterArray3D($key, [
        'text',
        'data'=>"callback_data",
        'text'=>"text",
        'callback_data'=>"callback_data",
        'url'=>"url",
        'switch_inline_query'=>"switch_inline_query",
        'inline'=>"switch_inline_query",
        'switch_inline_query_current_chat' => 'switch_inline_query_current_chat',
        'inline_this' => 'switch_inline_query_current_chat',
        'inlinethis' => 'switch_inline_query_current_chat',
        'inlineThis' => 'switch_inline_query_current_chat',
        'request_contact' => 'request_contact',
        'contact' => "request_contact",
        "request_location" => "request_location",
        "location" => "request_location",
        "request_poll" => "requset_poll",
        "poll" => "request_poll"
    ], null, true, true, false)) === false)
        mmb_error_throw("Invalid keyboard");
    if($inline === null){
        if($key != null)
            $inline = @isset($key[0][0]['callback_data']) || @isset($key[0][0]['url']) || @isset($key[0][0]['switch_inline_query']) || @isset($key[0][0]['switch_inline_query_current_chat']);
    }
    $a = [($inline?"inline_":"")."keyboard" => $key];
    if(!$inline && $resize) $a['resize_keyboard'] = $resize;
    if($once) $a['one_time_keyboard'] = true;
    if($selective) $a['selective'] = true;
    if($encode)
        $a = json_encode($a);
    return $a;
}

function mPers($ar){
    if(($ar = filterArray($ar, [
        'sendmsg' => 'can_send_messages',
        'sendmedia' => 'can_send_media_messages',
        'sendpoll' => 'can_send_polls',
        'sendother' => 'can_send_other_messages',
        'webpre' => 'can_add_web_page_previews',
        'changeinfo' => 'can_change_info',
        'invite' => 'can_invite_users',
        'pin' => 'can_pin_messages',

        'managechat' => 'can_manage_chat',
        'delete' => 'can_delete_messages',
        'managevoicechat' => 'can_manage_voice_chats',
        'restrict' => 'can_restrict_members',
        'promote' => 'can_promote_members',
        'post' => 'can_post_messages',
        'editpost' => 'can_edit_messages',
        'edit' => 'can_edit_messages',
        'anonymous' => 'is_anonymous',

        'can_send_messages' => 'can_send_messages',
        'can_send_media_messages' => 'can_send_media_messages',
        'can_send_polls' => 'can_send_polls',
        'can_send_other_messages' => 'can_send_other_messages',
        'can_add_web_page_previews' => 'can_add_web_page_previews',
        'can_change_info' => 'can_change_info',
        'can_invite_users' => 'can_invite_users',
        'can_pin_messages' => 'can_pin_messages',
        'can_manage_chat' => 'can_manage_chat',
        'can_delete_messages' => 'can_delete_messages',
        'can_manage_voice_chats' => 'can_manage_voice_chats',
        'can_restrict_members' => 'can_restrict_members',
        'can_promote_members' => 'can_promote_members',
        'can_post_messages' => 'can_post_messages',
        'can_edit_messages' => 'can_edit_messages',
        'is_anonymous' => 'is_anonymous',
    ])) === false)
        mmb_error_throw("Invalid permission array");
    return $ar;
}

function mInlineRes($results){
    $r = [];
    foreach($results as $res){
        $r[] = mInlineRes_A($res);
    }
    return json_encode($r);
}

function mInlineRes_A($data){
    if(($data = filterArray($data, [
        'id' => "id",
        'title' => "title",
        'msg' => "msg",
        'message' => "msg",
        'thumb' => "thumb",
        'cache' => "thumb",
        'des' => "des",
        'description' => "des",
        'photo' => "photo",
        'gif' => "gif",
        'mpeg4' => "mpeg4",
        'video' => "video",
        'audio' => "audio",
        'voice' => "voice",
        'doc' => "doc",
        'document' => "doc",
        'file' => "doc",
        //'location' => "location',"
        'contact' => "contact",
        'first' => 'first',
        'last' => 'last',
        'name' => "name",
    ])) === false)
        mmb_error_throw("Invalid inline query results data");
    $id = $data['id'] ?? rand(100000, 999999);
    $type = '';
    $media = '';
    $media_id = false;
    if(isset($data['photo'])){
        $type = 'photo';
        $media = $data['photo'];
    }
    elseif(isset($data['gif'])){
        $type = 'gif';
        $media = $data['gif'];
    }
    elseif(isset($data['mpeg4'])){
        $type = 'mpeg4_gif';
        $media = $data['mpeg4'];
    }
    elseif(isset($data['video'])){
        $type = 'video';
        $media = $data['video'];
    }
    elseif(isset($data['audio'])){
        $type = 'audio';
        $media = $data['audio'];
    }
    elseif(isset($data['voice'])){
        $type = 'voice';
        $media = $data['voice'];
    }
    elseif(isset($data['doc'])){
        $type = 'doc';
        $media = $data['doc'];
    }
    elseif(isset($data['contact'])){
        $type = 'contact';
    }
    else{
        $type = 'article';
    }
    if($media){
        if(is_string($media) && strpos($media, "://") === false){
            $media_id = true;
        }
    }

    $res = [
        'id' => $id,
        'type' => $type,
        'description' => $data['des'] ?? ""
    ];
    if($type == 'article'){
        $res['title'] = $data['title'] ?? "Untitled";
    }
    if($media){
        if(isset($data['title']))
            $res['title'] = $data['title'];
        if($type == 'mpeg4_gif'){
            $res['mpeg4' . ($media_id ? '_file_id' : '_url')] = $media;
        }
        else{
            $res[$type . ($media_id ? '_file_id' : '_url')] = $media;
        }
    }
    elseif($type == 'contact'){
        $res['contact'] = $data['contact'];
        if($data['name']){
            $f = $data['name'];
            $_ = strpos($f, " ");
            if($_ === false){
                $l = null;
            }
            else{
                $l = substr($f, $_ + 1);
                $f = substr($f, 0, $l);
            }
        }
        else{
            if(isset($data['first'])){
                $f = $data['first'];
                $l = $data['last'] ?? null;
            }
            elseif(isset($data['last'])){
                $f = $data['last'];
                $l = null;
            }
            else{
                $f = "Untitled";
                $l = null;
            }
        }
        $res['first_name'] = $f;
        $res['last_name'] = $l;
    }

    $msg = $data['msg'] ?? [];
    if(($msg = filterArray($msg, [
        'text' => "text",
        'caption' => "text",
        'mode' => "mode",
        'parse_mode' => "mode",
        'parsemode' => "mode",
        'diswebpre' => "disw",
        "disable_web_page_preview" => "disw",
        'key' => 'key',
    ])) === false)
        mmb_error_throw("Invalid inline query results message data");
    if($media){
        $res['caption'] = $msg['text'] ?? "";
        if($_ = $msg['parse_mode'] ?? null){
            $res['parse_mode'] = $_;
        }
    }
    elseif($type == 'article'){
        $cn = [
            'message_text' => $msg['text'] ??  "Untitled"
        ];
        if($_ = $msg['disw'] ?? null){
            $cn['disable_web_page_preview'] = $_;
        }
        if($_ = $msg['parse_mode'] ?? null){
            $cn['parse_mode'] = $_;
        }
        $res['input_message_content'] = $cn;
    }
    if($_ = $msg['key'] ?? false)
        $res['reply_markup'] = mkey($_, true, true, false);

    if($media){
        if(!$media_id){
            $res['thumb_url'] = $data['thumb'] ?? $media;
        }
    }
    elseif(isset($data['thumb'])){
        $res['thumb_url'] = $data['thumb'];
    }

    return $res;
}

function filterArray($array, $keys, $vals=null, $delEmpties1 = false){
    if($keys == null)
        $a = "n";
    elseif(gettype($keys) == "array")
        $a = "a";
    else
        $a = "c";
    if($vals == null)
        $b = "n";
    elseif(gettype($vals) == "array")
        $b = "a";
    else
        $b = "c";
    $r = [];
    foreach($array as $key => $val){
        if($delEmpties1 && $val == null) continue;
        if($a == "a"){
            if(isset($keys[$key]))
                $key = $keys[$key];
            elseif(($_ = strtolower($key)) && isset($keys[$_]))
                $key = $keys[$_];
            else
                return false;
        }elseif($a == "c"){
            $key = $keys($key);
            if($key === false)
                return false;
        }
        if($b == "a"){
            if(isset($vals[$val]))
                $val = $vals[$val];
        }elseif($b == "c"){
            $val = $vals($key, $val);
        }
        $r[$key] = $val;
    }
    return $r;
}

function filterArray2D($array, $keys, $vals=null, $delEmpties2 = false, $delEmpties1 = false){
    $new = [];
    foreach($array as $i => $val){
        if($delEmpties2 && $val == null) continue;
        if(($a = filterArray($val, $keys, $vals, $delEmpties1)) === false)
            return false;
        if($delEmpties2 && !$a) continue;
        $new[] = $a;
    }
    return $new;
}

function filterArray3D($array, $keys, $vals=null, $delEmpties3 = false, $delEmpties2 = false, $delEmpties1 = false){
    $new = [];
    foreach($array as $i => $val){
        if($delEmpties3 && $val == null) continue;
        if(($a = filterArray2D($val, $keys, $vals, $delEmpties2, $delEmpties1)) === false)
            return false;
        if($delEmpties3 && !$a) continue;
        $new[] = $a;
    }
    return $new;
}

function mmb_log($text){
    if(Mmb::$LOG)
        error_log($text, 0);
        //file_put_contents("mmb_log", "\n[" . date("Y/m/d H:i:s") . "] $text", FILE_APPEND);
    return $text;
}

function mmb_error_throw($des, $must_throw_error = false){
    /*if(Mmb::$LOG)
        mmb_log($des);*/
    if($must_throw_error || Mmb::$HARD_ERROR)
        throw new MmbException($des);
}

/**
 * محدود کردن اجرای کد
 *
 * @param bool|mixed $condition
 * @return bool
 */
function limit($condition){
    if($condition) return true;
    else{
        die;
    }
}

/**
 * سریالایز کردن تابع
 *
 * @param Closure $closure
 * @return string
 */
function serializeClosure(Closure $closure){
    require_once __DIR__ . '/Opis.php';
    return serialize(new \Opis\Closure\SerializableClosure($closure));
}

/**
 * انسریالایز کردن تابع
 *
 * @param string $closure
 * @return \Opis\Closure\SerializableClosure
 */
function unserializeClosure(string $closure){
    require_once __DIR__ . '/Opis.php';
    return unserialize($closure);
}

/**
 * با صرف نظر کردن از بزرگی و کوچکی حروف، دو رشته را با هم مقایسه می کند
 *
 * @param string $value1
 * @param string ...$values
 * @return bool
 */
function eqi($value1, $value2){
    return strtolower($value1) == strtolower($value2);
}

/**
 * محدود کردن عدد در بازه
 * 
 * با کمک این تابع می تواانید رنجی را مشخص کنید تا عدد شما بزرگ تر یا کوچک تر از این رنج نباشند. در نهایت این تابع یا خود عدد، یا حداکثر و یا حداقل را به شما می دهد
 *
 * @param int|float $number
 * @param int|float $min
 * @param int|float $max
 * @return int|float
 */
function clamp($number, $min, $max) {
    if($number > $max) return $max;
    if($number < $min) return $min;
    return $number;
}

/**
 * حذف پوشه و محتویات آن
 *
 * @param string $dirPath
 * @return bool
 */
function delDir($dirPath) {
    if(!is_dir($dirPath))
        return false;

    $files = scandir($dirPath);
    foreach($files as $file) {
        if($file == '.' || $file == '..') continue;
        $path = "$dirPath/$file";
        if(is_dir($path))
            delDir($path);
        else
            unlink($path);
    }
    return rmdir($dirPath);
}


/**
 * انکد کردن کاراکتر ها برای مد اچ تی ام ال تلگرام
 *
 * @param string $text
 * @return string
 */
function htmlEncode($text){
    return str_replace([
        '&', '<', '>',
    ], [
        "&amp;", "&lt;", "&gt;",
    ], $text);
}

/**
 * انکد کردن کاراکتر ها برای مد مارک داون تلگرام
 *
 * @param string $text
 * @return string
 */
function markdownEncode($text){
    return str_replace([
        "\\", '_', '*', '`', '['
    ], [
        "\\\\", "\\_", "\\*", "\\`", "\\[",
    ], $text);
}

/**
 * انکد کردن کاراکتر ها برای مد مارک داون2 تلگرام
 *
 * @param string $text
 * @return string
 */
function markdown2Encode($text){
    return preg_replace('/[\\\\_\*\[\]\(\)~`>\#\+\-=\|\{\}\.\!]/', '\\\\$0', $text);
}

/**
 * بررسی می کند رشته اصلی با رشته دیگری شروع می شود یا نه
 *
 * @param string $string
 * @param string $needle
 * @param boolean $ignoreCase
 * @return bool
 */
function startsWith($string, $needle, $ignoreCase = false) {
    $s = @substr($string, 0, strlen($needle));
    if($ignoreCase)
        return eqi($s, $needle);
    else
        return $s == $needle;
}

/**
 * بررسی می کند رشته اصلی با رشته دیگری به پایان میرسد یا نه
 *
 * @param string $string
 * @param string $needle
 * @param boolean $ignoreCase
 * @return bool
 */
function endsWith($string, $needle, $ignoreCase = false) {
    $s = @substr($string, -strlen($needle));
    if($ignoreCase)
        return eqi($s, $needle);
    else
        return $s == $needle;
}

/**
 * تغییر نوع آبجکت به کلاسی دیگر
 *
 * @param mixed $object
 * @param string $className
 * @return mixed
 */
function cast($object, $className) {
    if(!class_exists($className))
        throw new InvalidArgumentException("Class '$className' is not exists");

    $type = gettype($object);
    if($type == "array") {
        return unserialize(sprintf(
            'O:%d:"%s"%s',
            strlen($className),
            $className,
            strstr(serialize($object), ':')
        ));
    }

    elseif($type == "object") {
        return unserialize(sprintf(
            'O:%d:"%s"%s',
            strlen($className),
            $className,
            strstr(strstr(serialize($object), '"'), ':')
        ));
    }

    else {
        throw new InvalidArgumentException("Cast '$type' is not supported");
    }
}

/**
 * دیکد کردن جیسون به کلاس دلخواه
 *
 * @param string $json
 * @param string $className
 * @return mixed
 */
function json_decode2($json, $className) {
    return cast(json_decode($json, true), $className);
}

/**
 * گرفتن آدرس مطلق
 *
 * @param string $path
 * @param string $sep
 * @return string
 */
function getAbsPath($path, $sep = '/') {
    $abs = realpath($path);
    if($abs) {
        $path = $abs;
    }
    else {
        if(@$path[0] != '/' && @$path[1] != ':') {
            $path = realpath('.') . "/" . $path;
        }

        if(strpos($path, '..') !== false) {
            $c = 1;
            while($c)
                $path = preg_replace('/(\/|\\\)[^\/\!\?\|\:\\\]+(\/|\\\)\.\./', '', $path, -1, $c);
        }
        $c = 1;
        while($c)
            $path = preg_replace('/(^|\/|\\\)\.($|\/|\\\)/', '/', $path, -1, $c);

    }

    if($sep == '/')
        $path = str_replace('\\', '/', $path);
    elseif($sep == '\\')
        $path = str_replace('/', '\\', $path);
    else
        $path = str_replace(['/', '\\'], $sep, $path);

    return $path;
}


/**
 * گرفتن آدرس نسبی
 *
 * @param string $path
 * @param string $base 
 * @return string
 */
function getRelPath($path, $base = null) {
    if($base == null) {
        $base = getAbsPath('.');
    }
    else $base = str_replace('\\', '/', getAbsPath($base));
    $path = getAbsPath($path);
    if(endsWith($base, '/')) $base = substr($base, 0, -1);

    $path = str_split($path);
    $base = str_split($base);
    $max = min(count($path), count($base));
    for($i = 0; $i < $max; $i++) {
        if($path[$i] == $base[$i]) {
            unset($path[$i], $base[$i]);
        }
        else break;
    }

    $path = join('', $path);
    $base = join('', $base);
    if(!$base && $path[0] == '/') {
        $path = substr($path, 1);
    }
    else {
        $sl = substr_count($base, '/') + 1;
        $back = str_repeat('../', $sl);
        $path = $back . $path;
    }

    return $path;
}

/**
 * حذف فاصله های ابتدا و انتها، بصورت خط به خط
 *
 * @param string $string
 * @param string $charlist
 * @return void
 */
function trim2(string $string, string $charlist = " \t\n\r\0\x0B") {
    $lines = explode("\n", $string);
    $lines = array_map(function($line) use(&$charlist) {
        return trim($line, $charlist);
    }, $lines);
    return trim(join("\n", $lines));
}

/**
 * زمان نسبی ای به تابع بدید تا بصورت فارسی فاصله زمانی را به شما بدهد
 * 
 * مثال های ورودی و خروجی:
 * * 5 => 5 ثانیه
 * * 60 => 1 دقیقه
 * * 65 => 1 دقیقه و 5 ثانیه
 *
 * @param integer $time_relative
 * @param integer $roundBase
 * @return string
 */
function timeFa($time_relative, $roundBase = -1) {
    // Time
    if($roundBase > -1) {
        $time_relative = round($time_relative / $roundBase) * $roundBase;
    }
    
    // Second
    $second = $time_relative % 60;
    $time_relative = ($time_relative - $second) / 60;
    if(!$time_relative) {
        if(!$second) $second = 1;
        return "$second ثانیه";
    }
    if($second) $second = " و $second ثانیه";
    else $second = "";
    
    // Minute
    $minute = $time_relative % 60;
    $time_relative = ($time_relative - $minute) / 60;
    if(!$time_relative) {
        return "$minute دقیقه$second";
    }
    if($minute) $minute = " و $minute دقیقه";
    else $minute = "";
    
    // Hour
    $hour = $time_relative % 24;
    $time_relative = ($time_relative - $hour) / 24;
    if(!$time_relative) {
        return "$hour ساعت$minute$second";
    }
    if($hour) $hour = " و $hour ساعت";
    else $hour = "";
    
    // Day
    $day = $time_relative;
    return "$day روز$hour$minute$second";
}

/**
 * این متغیر یک تابع است که مقدار ورودی خود را بر می گرداند
 * 
 * از این متغیر در بین رشته ها استفاده کنید
 * * `"Hello {$f('World')}"`
 * * `"List: {$f(join($array))}"`
 * 
 * @var Closure
 */
global $f;
$f = function(...$values) {
    return join($values, ' ');
};
<?php

// Copyright (C): t.me/MMBlib

class Inline extends MmbBase implements IUser{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var MMB
     */
    private $_base;
    /**
     * Inline query id
     * آیدی اینلاین کوئری
     *
     * @var string
     */
    public $id;
    /**
     * From user
     * از طرف کاربر
     *
     * @var User
     */
    public $from;
    /**
     * Query
     * کوئری
     *
     * @var string
     */
    public $query;
    /**
     * Offset
     * 
     *
     * @var 
     */
    public $offset;
    /**
     * Chat type
     * نوع چت
     *
     * @var string
     */
    public $type;
    public const TYPE_PRIVATE = 'private';
    public const TYPE_GROUP = 'group';
    public const TYPE_SUPERGROUP = 'supergroup';
    public const TYPE_CHANNEL = 'channel';
    /**
     * Is chat type, private
     * آیا در چت خصوصی درخواست انجام شده
     *
     * @var bool
     */
    public $isPrivate;
    /**
     * Is chat type, group or supergroup
     * آیا در گروه یا سوپر گروه درخواست انجام شده
     *
     * @var bool
     */
    public $isGroup;
    /**
     * Is chat type, channel
     * آیا در کانال درخواست انجام شده
     *
     * @var bool
     */
    public $isChannel;

    function __construct($in, $base){
        $this->_base = $base;
        $this->id = $in['id'];
        $this->from = new user($in['from'], $base);
        $this->query = $in['query'];
        $this->offset = $in['offset'];
        $this->type = @$in['type'];
        $this->isPrivate = $this->type == self::TYPE_PRIVATE;
        $this->isGroup = $this->type == self::TYPE_GROUP || $this->type == self::TYPE_SUPERGROUP;
        $this->isChannel = $this->type == self::TYPE_CHANNEL;
    }
    
    /**
     * Answer inline query
     * پاسخ به اینلاین کوئری
     *
     * @param array $results
     * @param array $args
     * @return bool
     */
    function answer($results, $args=[]){
        if(isset($results['results']))
            $args = array_merge($results, $args);
        else
            $args['results'] = $results;
        $args['id'] = $this->id;
        return $this->_base->call('answerinlinequery', $args);
    }
    
    public function __getUserID()
    {
        return $this->from->id;
    }
    
}

class ChosenInline extends MmbBase implements IUser, IMsg{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var Mmb
     */
    private $_base;
    /**
     * Selected id
     * آیدی ایتم انتخاب شده
     *
     * @var string
     */
    public $id;
    /**
     * User
     * کاربر
     *
     * @var User
     */
    public $from;
    /**
     * Message id
     * شناسه پیام
     *
     * @var string
     */
    public $msgID;
    /**
     * Fake message
     * پیام فیک
     *
     * @var Msg
     */
    public $msg;
    /**
     * Query
     * پیام درخواست اینلاین
     *
     * @var string
     */
    public $query;
    public function __construct($r, $base)
    {
        $this->_base = $base;
        $this->id = $r['result_id'];
        $this->from = new User($r['from'], $base);
        $this->msgID = @$r['inline_message_id'];
        if($this->msgID)
            $this->msg = new Msg($this->msgID, $base, true);
        $this->query = $r['query'];
    }

    public function __getMsgID()
    {
        return $this->msg->id;
    }

    public function __getUserID()
    {
        return $this->from->id;
    }
    
}
<?php

// Copyright (C): t.me/MMBlib

interface IChat{
    /**
     * گرفتن شناسه چت
     *
     * @return mixed
     */
    public function __getChatID();
}

interface IUser{
    /**
     * گرفتن شناسه کاربر
     *
     * @return mixed
     */
    public function __getUserID();
}

interface IMsg{
    /**
     * گرفتن شناسه چت
     *
     * @return mixed
     */
    public function __getMsgID();
}

interface IMsgData{
    /**
     * گرفتن شناسه فایل
     *
     * @return mixed
     */
    public function __getMsgDataID();
}
<?php

// Copyright (C): t.me/MMBlib

class Listeners{

    private static $onUpd = [];
    private static $onUpdQueue = [];
    /**
     * افزودن شنونده ی آپدیت
     *
     * @param Closure|string|array $callback `function(Upd $upd)`
     * @param boolean $queue قرار گیری در صف
     * @return void
     */
    public static function onUpd($callback, $queue = false){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        if($queue)
            self::$onUpdQueue[] = $callback;
        else
            self::$onUpd[] = $callback;
    }

    public static function __runUpd(Upd $upd){
        return self::__runQ(self::$onUpd, self::$onUpdQueue, $upd);
    }

    private static $onMmbReq = [];
    private static $onMmbReqQueue = [];
    /**
     * افزودن شنونده ی درخواست به تلگرام، قبل از تبدیل ورودی ها
     *
     * @param Closure|string|array $callback `function(string &$method, array &$params)`
     * @param boolean $queue قرار گیری در صف
     * @return void
     */
    public static function onMmbReq($callback, $queue = false){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        if($queue)
            self::$onMmbReqQueue[] = $callback;
        else
            self::$onMmbReq[] = $callback;
    }
    public static function __runMmbReq(string &$method, array &$params){
        return self::__runQ2(self::$onMmbReq, self::$onMmbReqQueue, $method, $params);
    }


    private static $onTelReq = [];
    private static $onTelReqQueue = [];
    /**
     * افزودن شنونده ی درخواست به تلگرام
     *
     * @param Closure|string|array $callback `function(string &$method, array &$params, mixed &$replace_result)`
     * @param boolean $queue قرار گیری در صف
     * @return void
     */
    public static function onTelReq($callback, $queue = false){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        if($queue)
            self::$onTelReqQueue[] = $callback;
        else
            self::$onTelReq[] = $callback;
    }

    public static function __runTelReq(string &$method, array &$params, &$replace_result){
        return self::__runQ2(self::$onTelReq, self::$onTelReqQueue, $method, $params, $replace_result);
    }


    private static $onMmbReqEnd = [];
    private static $onMmbReqEndQueue = [];
    /**
     * افزودن شنونده ی درخواست به تلگرام، بعد از دریافت پاسخ تلگرام
     *
     * @param Closure|string|array $callback `function(&$result, string $method, array $params)`
     * @param boolean $queue قرار گیری در صف
     * @return void
     */
    public static function onMmbReqEnd($callback, $queue = false){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        if($queue)
            self::$onMmbReqEndQueue[] = $callback;
        else
            self::$onMmbReqEnd[] = $callback;
    }

    public static function __runMmbReqEnd(&$result, string $method, array $params){
        return self::__runQ2(self::$onMmbReqEnd, self::$onMmbReqEndQueue, $result, $method, $params);
    }


    private static $onStart = [];
    /**
     * افزودن شنونده ی شروع اتم
     *
     * @param Closure|string|array $callback
     * @return void
     */
    public static function onStart($callback){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        self::$onStart[] = $callback;
    }

    public static function __runStart(){
        foreach(self::$onStart as $callback){
            $callback();
        }
    }


    private static $onReady = [];
    /**
     * افزودن شنونده ی آماده بودن ام ام بی - مقدار دهی شدن کلاس ها و پلاگین ها
     *
     * @param Closure|string|array $callback
     * @return void
     */
    public static function onReady($callback){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        self::$onReady[] = $callback;
    }

    public static function __runReady(){
        foreach(self::$onReady as $callback){
            $callback();
        }
    }

    private static $onFinishIsSet = false;
    private static $onFinish = [];
    /**
     * افزودن شنونده ی پایان اسکریپت
     *
     * @param Closure|string|array $callback
     * @return void
     */
    public static function onFinish($callback) {
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        static::$onFinish[] = $callback;
        if(!static::$onFinishIsSet) {
            static::$onFinishIsSet = true;
            register_shutdown_function([staic::class, '__runFinish']);
        }
    }

    public static function __runFinish(){
        foreach(self::$onFinish as $callback){
            $callback();
        }
    }


    /**
     * افزودن شنونده ی نصب دیتابیس
     * 
     * از این شنونده برای افزودن تیبل ها و ردیف های خود استفاده کنید
     * * callback: `function (&$tables) { ... }`
     *
     * @param MmbDbBase $db
     * @param Closure|string|array $callback
     * @return void
     */
    public static function onDbInstallation(MmbDbBase $db, $callback){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        $db->__listeners[] = $callback;
    }

    public static function __runDbInstallation(MmbDbBase $db, &$tables){
        foreach($db->__listeners as $callback){
            $callback($tables);
        }
    }

    /**
     * افزودن شنونده ی نصب دیتابیس به دیتابیس اتم تنظیم شده
     * 
     * از این شنونده برای افزودن تیبل ها و ردیف های خود استفاده کنید
     * * callback: `function (&$tables) { ... }`
     *
     * @param Closure|string|array $callback
     * @return void
     */
    public static function onAtomDbInstallation($callback) {
        if(Atom::$db && Atom::$db instanceof MmbDbBase) {
            self::onDbInstallation(Atom::$db, $callback);
        }
    }


    
    private static function __runQ(&$callbacks, &$callbacksQueue, ...$args){
        $continue = true;
        foreach($callbacks as $callback){
            if($callback(...$args) === false)
                $continue = false;
        }
        if($continue)
        foreach($callbacksQueue as $callback){
            if($callback(...$args) === false)
                return false;
        }
        return $continue;
    }

    private static function __runQ2(&$callbacks, &$callbacksQueue, &...$args){
        $continue = true;
        foreach($callbacks as $callback){
            if($callback(...$args) === false)
                $continue = false;
        }
        if($continue)
        foreach($callbacksQueue as $callback){
            if($callback(...$args) === false)
                return false;
        }
        return $continue;
    }
    



    private static $all = [];
    private static $all_queue = [];

    /**
     * افزودن شنونده دلخواه با نام
     *
     * @param string $name نام شنونده
     * @param Closure|string|array $callback `function(...)`
     * @param boolean $queue قرار گیری در صف
     * @return void
     */
    public static function listen($name, $callback, $queue = false){
        if(!($callback instanceof Closure || is_callable($callback))){
            throw new MmbException("The callback type is invalid, Callable/Closure required");
        }
        if($queue)
            self::$all_queue[$name][] = $callback;
        else
            self::$all[$name][] = $callback;
    }

    /**
     * اجرای شنونده های دلخواه
     *
     * @param string $name نام شنونده
     * @param mixed ...$args
     * @return bool
     */
    public static function run($name, ...$args) {
        $continue = true;
        foreach(self::$all[$name]??[] as $callback){
            if($callback(...$args) === false)
                $continue = false;
        }
        if($continue)
        foreach(self::$all_queue[$name]??[] as $callback){
            if($callback(...$args) === false)
                return false;
        }
        return $continue;
    }

    /**
     * اجرای شنونده های دلخواه (2)
     *
     * @param string $name نام شنونده
     * @param mixed &...$args
     * @return bool
     */
    public static function run2($name, &...$args) {
        $continue = true;
        foreach(self::$all[$name]??[] as $callback){
            if($callback(...$args) === false)
                $continue = false;
        }
        if($continue)
        foreach(self::$all_queue[$name]??[] as $callback){
            if($callback(...$args) === false)
                return false;
        }
        return $continue;
    }

}
<?php

// Copyright (C): t.me/MMBlib

define('MMB_VERSION', '3.1');

// Telegram IP
if(isset($_SERVER['REMOTE_ADDR'])){
    $telegram_ip_ranges = [
        ['lower' => '*************', 'upper' => '***************'],
        ['lower' => '**********',    'upper' => '************'],
    ];

    $ip_dec = (float) sprintf("%u", ip2long($_SERVER['REMOTE_ADDR']));
    $ok=false;
    foreach ($telegram_ip_ranges as $telegram_ip_range)
    {
        $lower_dec = (float) sprintf("%u", ip2long($telegram_ip_range['lower']));
        $upper_dec = (float) sprintf("%u", ip2long($telegram_ip_range['upper']));
        if ($ip_dec >= $lower_dec and $ip_dec <= $upper_dec)
        {
            $ok=true;
            break;
        }
    }
    define('TELEGRAM_IP', $ok);
    unset($ok, $ip_dec, $telegram_ip_ranges, $telegram_ip_range, $lower_dec, $upper_dec);
}
else define('TELEGRAM_IP', false);


// Includes
$includes = [
    __DIR__ . '/Interface.php',
    __DIR__ . '/Storage.php',
    __DIR__ . '/Tools.php',
    __DIR__ . '/Listener.php'
];
foreach($includes as $include){
    require_once $include;
}
$includes = array_diff(glob(__DIR__ . '/*.php'), [
    __FILE__,
    __DIR__ . '/Atom.php',
    __DIR__ . '/403.php'
], $includes);
foreach($includes as $include){
    require_once $include;
}
unset($includes);


// Classes

class MmbBase{

    /**
     * اجرای اسلیپ و برگرداندن خود
     *
     * @param float $seconds
     * @return $this
     */
    public function sleep($seconds){
        sleep($seconds);
        return $this;
    }

}

class Mmb extends MmbBase implements Serializable{
    
    // Setings and private values:
    public static $HARD_ERROR = true;
    public static $LOG = true;
    public static $_BOTS = [];
    public const VERSION = MMB_VERSION;
    private $_token;
    public $SWebhook = true;
    public $SUpdateF = 0;

    /**
     * @var $this
     */
    public static $this = null;

    /**
     * Initialize new MMB object
     * مقدار دهی و ساخت یک شی MMB
     *
     * @param string $token
     */
    function __construct(string $token){
        $token = trim($token);
        $this->_token = $token;
        $this->_c = false;
        //$this->mmb_dir(true);
        self::$_BOTS[] = $this;
        if(!self::$this){
            self::$this = $this;
        }
    }
    
    public $_c = false;
    /**
     * Enable or disable MMB dir
     * تنظیم فعال یا غیر فعال بودن پوشه ام.ام.بی
     * 
     * (تابع منسوخ شده)
     *
     * @param bool $enabled
     * @return void
     */
    function mmb_dir(bool $enabled){
        $this->_c = $enabled;
        if($enabled)
            if(!file_exists(__DIR__ . "/MMB")){
                mkdir(__DIR__ . "/MMB");
                file_put_contents(__DIR__ . "/MMB/index.php", "<html><body><center><h1>MMB</h1></center></body></html>");
                file_put_contents(__DIR__ . "/MMB/.htaccess", "<IfModule mod_rewrite.c>\nRewriteEngine On\n\nRewriteRule ^.+$ index.php [NC,QSA]\n</IfModule>");
            }
    }
    
    /**
     * Send a request to telegram API with normal method and args
     * ارسال درخواست به API تلگرام با متد و پارامتر های عادی
     *
     * @param string $method
     * @param array $args
     * @return stdClass|false
     */ 
    function bot(string $method, array $args=[]){
        $method = str_replace(["-", "_", " ", "\n", "\t", "\r"], '', $method);

        $replace_result = false;
        if(!Listeners::__runTelReq($method, $args, $replace_result)) {
            return $replace_result;
        }

        if($this->midd){
            $url = $this->midd;
            $args['method'] = $method;
            $args['token'] = $this->_token;
        }else
        $url = "https://api.telegram.org/bot".$this->_token."/".$method;
        $ch = curl_init();
        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch,CURLOPT_POSTFIELDS,$args);
        $res = curl_exec($ch);
        if(curl_error($ch)){
            return false;
        }else{
            $r = json_decode($res);
            return $r;
        }
    }
    
    /**
     * Send a request to telegram API with normal method and mmb args
     * ارسال درخواست به ای پی آی تلگرام با متد عادی و پارامتر های ام.ام.بی
     *
     * @param string $method
     * @param array $args
     * @param boolean $throw_error
     * @return array|false
     */
    function call(string $method, array $args=[]){
        $method = str_replace(["-", "_", " ", "\n", "\t", "\r"], '', $method);

        if(!Listeners::__runMmbReq($method, $args)) {
            return false;
        }

        $datas=[];
        static $k = [
            "id"=>[
                    "chat_id",
                    'answercallbackquery'=>"callback_query_id",
                    'answerinlinequery'=>"inline_query_id",
                    'getfile'=>"file_id"
                ],
                'chat'=>"chat_id",
                "chatid"=>"chat_id",
                "text"=>[
                    "text",
                    "copymessage" => "caption",
                    "sendpoll" => 'question'
                ],
                "msg"=>"message_id",
                "msgid"=>"message_id",
                "messageid" => "message_id",
                "mode"=>"parse_mode",
                "parsemode" => "parse_mode",
                "reply"=>"reply_to_message_id",
                "replytomsg"=>"reply_to_message",
                "key"=>"reply_markup",
                'filter'=>"allowed_updates",
                'offset'=>"offset",
                'limit'=>[
                    "limit",
                    'createchatinvitelink' => "member_limit",
                    'editchatinvitelink' => "member_limit"
                ],
                "link" => 'invite_link',
                "invite" => 'invite_link',
                "invitelink" => "invite_link",
                "memberlimit" => "member_limit",
                'alert'=>"show_alert",
                "showalert" => "show_alert",
                'from'=>"from_chat_id",
                "fromchat" => "from_chat_id",
                'user'=>"user_id",
                'caption'=>"caption",
                "results"=>"results",
                'url'=>"url",
                "until"=>"until_date",
                'per'=>"permissions",
                'action'=>"action",
                'photo'=>"photo",
                'doc'=>"document",
                'document'=>"document",
                'voice'=>"voice",
                'audio'=>"audio",
                'video'=>"video",
                'media'=>"media",
                'anim'=>"animation",
                'animation'=>"animation",
                'sticker' => "sticker",
                'videonote'=>"video_note",
                //"canedit"=>"can_be_edited",
                //"canpostmsg"=>"can_post_messages",
                //'caneditmsg'=>"can_edit_messages",
                //'candelmsg'=>"can_delete_messages",
                //'canrestrict'=>"can_restrict_members",
                //'canpromote'=>"can_promote_members",
                //'canchangeinfo'=>"can_change_info",
                //'canpinmsg'=>"can_pin_messages",
                //'cansendmsg'=>"can_send_messages",
                //'cansendmedia'=>"can_send_media_messages",
                //'cansendpoll'=>"can_send_poll_messages",
                //'cansendothermsg'=>"can_send_other_messages",
                //'canaddwebpre'=>"can_add_web_page_previews",
                //'caninvite'=>"can_invite_users",
                'diswebpre'=>"disable_web_page_preview",
                'disnotif'=>"disable_notification",
                'phone'=>"phone_number",
                'name'=>"first_name",
                'firstname'=>"first_name",
                'first'=>"first_name",
                'lastname'=>"last_name",
                'last'=>"last_name",
                'title'=>"title",
                "performer" => "performer",
                "perf" => "performer",
                'des'=>"description",
                'setname'=>"sticker_set_name",
                'set_name'=>"sticker_set_name",
                'cache'=>"cache_time",
                'cachetime'=>"cache_time",
                "personal" => "is_personal",
                "ispersonal" => "is_personal",
                "nextoffset" => "next_offset",
                "switchpmtext" => "switch_pm_text",
                "switchpmparameter" => "switch_pm_parameter",
                "cmds"=>"commands",
                "inlinemsg"=>"inline_message_id",
                // 'name'=>"name", // Note
                "expire" => [
                    "expire_date",
                    'sendpoll' => 'close_date'
                ],
                "joinreq" => "creates_join_request",
                "joinrequest" => "creates_join_request",
                'drop' => "drop_pending_updates",
                'question' => 'question',
                'options' => 'options',
                'isanonymous' => 'is_anonymous',
                'anonymous' => 'is_anonymous',
                'type' => 'type',
                'allowmultiple' => 'allows_multiple_answers',
                'multiple' => 'allows_multiple_answers',
                'explan' => 'explanation',
                'explanmode' => 'explanation_parse_mode',
                'preiod' => 'open_preiod',
                'timer' => 'open_preiod',
                'emoji' => 'emoji',
                'correct' => 'correct_option_id',
                "allowsendingwithoutreply" => "allow_sending_without_reply",
                "ignorerep" => "allow_sending_without_reply",

                "ignore" => "ignore",
            ];
        static $kobj = [
            'from_chat_id' => 'IChat',
            'chat_id' => 'IChat',
            'user_id' => 'IUser',
            'message_id' => 'IMsg',
            'reply_to_message_id' => 'IMsg',
            'photo' => "IMsgData",
            'doc' => "IMsgData",
            'document' => "IMsgData",
            'voice' => "IMsgData",
            'audio' => "IMsgData",
            'video' => "IMsgData",
            'media' => "IMsgData",
            'anim' => "IMsgData",
            'animation' => "IMsgData",
            'sticker' => "IMsgData",
            'videonote' => "IMsgData",
            'file_id' => 'IMsgData',
            'callback_query_id' => 'Callback',
            'inline_query_id' => 'Inline',
        ];
        foreach($args as $name => $val){
            if($val === null) continue;
            $real_name = $name;
            $name = str_replace(["_", "-", " ", "\r", "\n", "\t"], "", strtolower($name));
            if($name == "key" && gettype($val) == "array") $val = mkey($val);
            elseif($name == "results" && gettype($val) == "array") $val = mInlineRes($val);
            elseif($name == "per"){
                if(gettype($val) == "array")
                    $val = mPers($val);
                if($method == "promoteChatMember"){
                    if(!is_array($val)){
                        if($val instanceof JsonSerializable) $val = mPers($val->jsonSerialize());
                        else
                            mmb_error_throw("Key '$name' is object! Array required");
                    }
                    foreach($val as $_k => $_v){
                        $datas[$_k] = $_v;
                    }
                    continue;
                }
            }
            elseif($name == "media"){
                $fil = ['type'=>"type", 'text'=>"caption", 'media'=>'media', 'mode'=>"parse_mode", 'thumb'=>"thumb", 'duration'=>"duration", 'title'=>"title", 'performer'=>"permorfer"];
                if($method == "sendmedia" || $method == "editmessagemedia"){
                    $val = filterArray($val, $fil);
                }else{
                    $val = filterArray2D($val, $fil);
                }
            }
            elseif($name == "text"){
                if(str_replace(["photo", "audio", "animation", "video", "voice", "Document", "media"], '', $method)!=$method) $name = "caption";
            }
            if(isset($k[$name])){
                if(gettype($k[$name])=="array"){
                    if(isset($k[$name][$method])) $name = $k[$name][$method];
                    else $name = $k[$name][0];
                }else
                    $name = $k[$name];
            }else
                mmb_error_throw("Invalid key '$real_name'");

            if(is_object($val)){
                if(isset($kobj[$name])){
                    switch($kobj[$name]){
                        case 'IChat':
                            if($val instanceof IChat) $val = $val->__getChatID();
                        break;
                        case 'IUser':
                            if($val instanceof IUser) $val = $val->__getUserID();
                        break;
                        case 'IMsg':
                            if($val instanceof IMsg) $val = $val->__getMsgID();
                        break;
                        case 'IMsgData':
                            if($val instanceof IMsgData) $val = $val->__getMsgDataID();
                        break;
                        case 'Callback':
                            if($val instanceof Callback) $val = $val->id;
                        break;
                        case 'Inline':
                            if($val instanceof Inline) $val = $val->id;
                        break;
                    }
                }
                if($val instanceof JsonSerializable){
                    $val = json_encode($val);
                }
                if(is_object($val) && !($val instanceof CURLFile)){
                    mmb_error_throw("Key '$real_name' is '" . get_class($val) . "' object!");
                }
            }
            if(is_array($val)) $val = json_encode($val);

            $datas[$name] = $val;
        }
        $ignore = false;
        if(isset($datas['ignore'])){
            $ignore = $datas['ignore'];
            unset($datas['ignore']);
        }

        $replace_result = false;
        if(!Listeners::__runTelReq($method, $datas, $replace_result)) {
            return $replace_result;
        }

        if($this->midd){
            $url = $this->midd;
            $datas['method'] = $method;
            $datas['token'] = $this->_token;
        }else
        $url = "https://api.telegram.org/bot".$this->_token."/".$method;
        $ch = curl_init();
        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch,CURLOPT_POSTFIELDS,$datas);
        if(self::$proxy_type){
            $ptype = self::$proxy_type;
            if($ptype == "HTTP"){
                curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
                curl_setopt($ch, CURLOPT_PROXY, self::$proxy_addr);
                curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
            }
            elseif($ptype == "HTTPS"){
                curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
                curl_setopt($ch, CURLOPT_PROXY, self::$proxy_addr);
                curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTPS);
            }
            elseif($ptype == "SOCKS5"){
                curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, 1);
                curl_setopt($ch, CURLOPT_PROXY, self::$proxy_addr);
                curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
            }
            if(self::$proxy_usps){
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, self::$proxy_usps);
            }
        }
        $res = curl_exec($ch);
        if(curl_error($ch)){
            $des = "Connection error";
        }else{
            $r = json_decode($res, true);
            if($r['ok'] === true) {
                Listeners::__runMmbReqEnd($r['result'], $method, $args);
                return $r['result'];
            }
            else $des = "Telegram error: ".$r['description'] . ": on $method";
        }
        if(!$ignore)
            mmb_error_throw($des);
        return false;
    }

    private static $proxy_type;
    private static $proxy_addr;
    private static $proxy_usps;
    public static function setProxy($type, $addr, $userpass = ""){
        $type = strtoupper($type);
        if(in_array($type, ['HTTP', 'HTTPS', 'SOCKS5'])){
            self::$proxy_type = $type;
            self::$proxy_addr = $addr;
            self::$proxy_usps = $userpass;
        }
        else{
            mmb_error_throw("Proxy type is not valid");
        }
    }
    public static function setHttpProxy($addr, $userpass = ""){
        self::setProxy("HTTP", $addr, $userpass);
    }
    public static function setHttpsProxy($addr, $userpass = ""){
        self::setProxy("HTTPS", $addr, $userpass);
    }
    public static function setSocks5Proxy($addr, $userpass = ""){
        self::setProxy("SOCKS5", $addr, $userpass);
    }
    
    private $midd=false;
    /**
     * Set Midd proxy
     * تنظیم پروکسی Midd
     *
     * @param string $link
     * @return void
     */
    function setMidd($link){
        $this->midd = $link;
    }

    /**
     * Update mmb to last version
     * بروز رسانی ام ام بی به اخرین نسخه
     *
     * @return bool
     */
    public static function updateMmb(){
        $v = file_get_contents('https://mmblib.ir/download/getlast.php', false, stream_context_create([
            'http' => [
                'timeout' => 10
            ]
        ]));
        if(!$v){
            return false;
        }
        else{
            $v = json_decode($v);
            if($v->version == MMB_VERSION){
                return false;
            }
            if($v->ext != "php"){
                return false;   // Unsupported
            }
            return copy($v->download, __FILE__, stream_context_create([
                'http' => [
                    'timeout' => 50
                ]
            ]));
        }
    }

    /**
     * Download plugin from mmblib.ir
     * دانلود پلاگین ام ام بی
     * 
     * @param string $name
     * @return bool
     *//* 
    public static function getPluginMmb($name){
        $p = file_get_contents('https://mmblib.ir/shop/getplugin.php?name=' . urlencode($name), false, stream_context_create([
            'http' => [
                'timeout' => 10
            ]
        ]));
        if(!$p){
            mmb_error_throw("Connection error on download plugin '$name'");
            return false;
        }
        $p = json_decode($p);
        if($p->ok){
            if(!is_dir("plugins")) mkdir("plugins");
            return copy($p->link, "plugins/plugin.$name.php");
        }
        else{
            if($p->error == "NAME"){
                mmb_error_throw("Plugin '$name' not found in mmblib.ir");
            }
            elseif($p->error == "PRICE"){
                mmb_error_throw("Please buy plugin '$name' from {$p->buy} and copy it here");
            }
            else{
                mmb_error_throw("Error on download plugin '$name': " . $p->error);
            }
            return false;
        }
    }*/

    private function getDataBR($id){
        if(!$this->_c) $this->mmb_dir(true);
        if(!file_exists(__DIR__ . "/MMB/$id.mmb")) return false;
        $content = file_get_contents(__DIR__ . "/MMB/$id.mmb");
        $b="";foreach(str_split($content)as$a)$b.=sprintf("%08b",ord($a));$b=strrev($b);$n="";foreach(str_split($b,8)as$a)$n.=chr(bindec($a));return $n;
    }
    
    /**
     * Save a text in MMB folder
     * ذخیره یک متن در پوشه ام.ام.بی
     *
     * @param string $name
     * @param string $text
     * @return void
     */
    function save(string $name, string $text){
        if(!$this->_c) $this->mmb_dir(true);
        $b="";foreach(str_split($text)as$a)$b.=sprintf("%08b",ord($a));$b=strrev($b);$n="";foreach(str_split($b,8)as$a)$n.=chr(bindec($a));
        file_put_contents(__DIR__ . "/MMB/$name.mmb", $n);
    }
    
    /**
     * Load a text from MMB folder
     * بارگذاری داده از پوشه ام.ام.بی
     *
     * @param string $name
     * @return string
     */
    function load($name){
        return $this->getDataBR($name);
    }
    
    private $_def = [];
    /**
     * Set default data(for MMB->getData & user->getData)
     * تنظیم مقدار پیش فرض دیتا
     *
     * @param mixed $def
     * @return void
     */
    function setDefData($def){
        $this->_def = $def;
    }

    /**
     * Get user or other data(numbers => user data, others => other data)
     * گرفتن دیتای کاربر یا چیز های دیگر(اعداد => کاربر، دیگر => دیگر)
     *
     * @param string $id
     * @return mixed
     */
    function getData($id){
        if(!is_numeric($id)) $u = "d";
        else $u = "u";
        $d = $this->load($u."_$id");
        if($d === false){
            if($u == "u")
                return $this->_def;
            else return [];
        }
        return json_decode($d, true);
    }
    /**
     * Set user or other data(numbers => user data, others => other data)
     * ذخیره دیتای کاربر یا چیز های دیگر(اعداد => کاربر، دیگر => دیگر)
     *
     * @param string $id
     * @param mixed $data
     * @return true
     */
    function setData($id, $data){
        if(!is_numeric($id)) $u = "d";
        else $u = "u";
        $this->save($u."_$id", json_encode($data));
        return true;
    }
    
    /**
     * Get all users that saved by 'setData' or 'getData'(seted default) functions
     * گرفتن تمامی کاربرانی که با تابع های 'setData' و 'getData'(ذخیره ی داده ی پیشفرض) ذخیره شدند
     *
     * @return array
     */
    function getUsers(){
        if(!$this->_c) $this->mmb_dir(true);
        $all = glob(__DIR__ . "/MMB/u_*.mmb");
        foreach($all as $a=>$b){
            $all[$a] = substr($b, 6, strlen($b)-10);
        }
        return $all;
    }
    /**
     * Check exists a data
     * بررسی وجود دیتای کاربر
     *
     * @param string $id
     * @return bool
     */
    function exData($id){
        if(!$this->_c) $this->mmb_dir(true);
        if(!is_numeric($id)) $u = "d";
        else $u = "u";
        return file_exists(__DIR__ . "/MMB/".$u."_$id.mmb");
    }
    /**
     * Check exists a data
     *
     * @param string $id
     * @return bool
     */
    function _ex($id){
        return file_exists(__DIR__ . "/MMB/$id.mmb");
    }
    /**
     * Delete a data
     * حذف دیتا
     *
     * @param string $id
     * @return bool
     */
    function _del($id){
        if(!$this->_c) $this->mmb_dir(true);
        return @unlink(__DIR__ . "/MMB/$id.mmb");
    }

    /**
     * Get input update
     * دریافت آپدیت ارسال شده
     *
     * @return Upd|null|false
     */
    function getUpd(){
        if(!TELEGRAM_IP) return false;
        if(Upd::$this !== null) return Upd::$this;
        if($this->SUpdateF){
            $_ = $this->SUpdateF;
            $upd = $_();
        }
        else{
            $upd = @file_get_contents("php://input");
        }
        if($upd == null){
            return $upd;
        }
        else{
            $u = json_decode($upd, true);
            if($u == false) return false;

            if(Background::isEnabled()){
                if(!Background::__($u,$this->_token))
                    return false;
            }
            else{
                if(!TELEGRAM_IP) return false;
            }

            $u = new upd($u, $this);
            //if(!$this->updListenersRun($u))
            //    return false;
            return $u;
        }
    }

    /**
     * این لینک را برای وبهوک تنظیم می کند
     *
     * @param array $array
     * @return void
     */
    public function setWebhookThis(array $args = []){
        $uri = @$_SERVER['SCRIPT_URI'];
        if($uri == "")
            $dm = "https://".$_SERVER['SERVER_NAME'].$_SERVER['SCRIPT_NAME'];
        else
            $dm = str_replace(["http://","Http://"], "https://", $uri);
        if(strlen($dm)>10){
            $args['url'] = $dm;
            $this->call('setwebhook', $args);
        }
    }
    
    
    private $_first=true;
    /**
     * Send request to telegram and get updates
     * ارسال درخواست به تلگرام و دریافت آپدیت ها
     *
     * @param int $offset
     * @param int $limit
     * @param array $filter
     * @return Upd[]
     */
    function getUpds($offset=false, $limit=10, $filter=null){
        if($this->_first){
            $web=$this->call('getwebhookinfo',[]);
            if($web['url']!=""){
                $this->call('deletewebhook',[]);
            }
            $this->_first = false;
        }
        if($offset == false){
            if(isset($this->_offset))
                $offset = $this->_offset;
            else
                $offset = -1;
        }
        try{
            $upds = $this->call('getupdates', [
                'offset'=>$offset,
                'limit'=>$limit,
                'filter'=>$filter
            ]);
            if($upds == []) return [];
            else{
                $r=[];
                foreach($upds as $upd){
                    $x = new upd($upd, $this);
                    //if(!$this->updListenersRun($x))
                    //    continue;
                    $this->_offset = $x->id + 1;
                    $r[] = $x;
                }
                return $r;
            }
        }catch(Exception $e){
            return [];
        }
    }
    
    /**
     * Add a job to job list
     * افزودن یک جاب یه لیست جاب ها
     *
     * @param mixed $jobData
     * @return void
     */
    function addJob($jobData){
        if(!$this->_c) $this->mmb_dir(true);
        //$this->mmb_dir(true);
        if(file_exists(__DIR__ . "/MMB/jobs.mmb")){
            $jobs = json_decode($this->load("jobs"), true);
        }
        else{
            $jobs = [];
        }
        $jobs[] = $jobData;
        $this->save("jobs", json_encode($jobs));
    }
    
    /**
     * Get next job and remove from job list
     * گرفتن جاب بعدی و حذف آن از لیست جاب ها
     *
     * @return mixed
     */
    function nextJob(){
        if(!file_exists(__DIR__ . "/MMB/jobs.mmb"))
            return false;
        $jobs = json_decode($this->load("jobs"), true);
        if(count($jobs)==0)
            return false;
        $job = $jobs[0];
        unset($jobs[0]);
        $jobs = array_values($jobs);
        $this->save("jobs", json_encode($jobs));
        return $job;
    }
    
    /**
     * Get count of job list
     * گرفتن تعداد جاب های لیست جاب
     *
     * @return int
     */
    function countJob(){
        if(!file_exists(__DIR__ . "/MMB/jobs.mmb"))
            return 0;
        $j = json_decode($this->load("jobs"), true);
        return count($j);
    }
    
    /**
     * Get robot public data
     * گرفتن اطلاعات عمومی ربات
     *
     * @param array $args
     * @return user
     */
    function getMe(array $args = []){
        return new user($this->call('getme', $args), $this);
    }
    
    /**
     * Answer callback query
     * پاسخ به کالبک
     * 
     * Arguments:
     *  id => Callback id | آیدی کالبک
     *  text => Text for show | متن نمایشی
     *  alert => Show alert | نمایش پیغام
     * 
     * @param array $args
     * @return bool
     */
    function answerCallback(array $args){
        return $this->call('answercallbackquery', $args);
    }
    
    /**
     * Send a message
     * ارسال پیام
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  text => Text | متن
     *  mode => Parse mode | مد متن
     *  key => Keyboard | دکمه ها
     * 
     * @param array $args
     * @return msg|false
     */
    function sendMsg(array $args){
        $r = $this->call('sendmessage', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Delete message
     * حذف پیام
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  msg => Message id | آیدی پیام
     * 
     * @param array $args
     * @return bool
     */
    function delMsg(array $args){
        return $this->call('deletemessage', $args);
    }

    /**
     * Send a media
     * ارسال رسانه
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  text => Caption | متن توضیحات
     *  media => Media | مدیا
     *  mode => Parse mode | مد متن
     *  key => Keyboard | دکمه ها
     * 
     * @param array $args
     * @return msg|false
     */
    function sendMedia(array $args){
        $r = $this->call('sendmedia', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Forward message without linking to original message
     * باز ارسال پیام بدون نام، می توانید محتویات مثل کپشن را جایگزین نیز کنید
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  from => From chat id | آیدی چتی که پیام در آن است
     *  msg => Message id | آیدی عددی پیام
     * 
     * @param array $args
     * @return msg|false
     */
    function copyMsg(array $args){
        $r = $this->call('copymessage', $args);
        if($r){
            if(!isset($r['chat'])){
                $r['chat'] = [
                    'id' => $args['chat'] ?? $args['id'] ?? $args['chat_id'] ?? $args['chatID'] ?? 0
                ];
            }
            return new msg($r, $this);
        }
        else
            return false;
    }

    /**
     * Forward message
     * باز ارسال پیام
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  from => From chat id | آیدی چتی که پیام در آن است
     *  msg => Message id | آیدی عددی پیام
     * 
     * @param array $args
     * @return msg|false
     */
    function forwardMsg(array $args){
        $r = $this->call('forwardmessage', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Send medias
     * ارسال آلبوم
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  text => Caption | متن توضیحات
     *  medias => Medias | مدیا ها
     *  mode => Parse mode | مد متن
     *  key => Keyboard | دکمه ها
     * 
     * @param array $args
     * @return msg|false
     */
    function sendMedias($args){
        $r = $this->call('sendmediagroup', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Send x
     * ارسال x
     * 
     * Arguments:
     *  id => Chat id | آیدی چت
     *  text => Caption or text | متن توضیحات یا متن اصلی
     *  val => Value | مقدار
     *  mode => Parse mode | مد متن
     *  key => Keyboard | دکمه ها
     * 
     * @param string|array $type
     * @param array $args
     * @return msg|false
     */
    function send($type, array $args=[]){
        if(gettype($type) == "array"){
            $args = array_merge($type, $args);
            $type = $args['type'];
            unset($args['type']);
        }
        if($type == "text"){
            unset($args['val']);
            $r = $this->call('sendmessage', $args);
        }else{
            if($type == "doc") $type = "Document";
            if($type == "anim") $type = "animation";
            if(isset($args['val']))
                $args[strtolower($type)] = $args['val'];
            unset($args['val']);
            $r = $this->call('send'.$type, $args);
        }
        if($r)
            return new msg($r, $this);
        else
            return false;
    }

    /**
     * Send dice
     * ارسال تاس
     *
     * @param array $args
     * @return Msg|false
     */
    public function sendDice($args){
        if(is_array($args))
            $r = $this->call('senddice', $args);
        else
            $r = $this->call('senddice', ['chat' => $args]);
        if($r)
            return new Msg($r, $this);
        else
            return false;
    }
    
    /**
     * Send poll
     * ارسال نظرسنجی
     *
     * @param array $args
     * @return Msg|false
     */
    public function sendPoll($args){
        $r = $this->call('sendpoll', $args);
        if($r)
            return new Msg($r, $this);
        else
            return false;
    }
    
    public const ACTION_TYPING = 'typing';
    public const ACTION_UPLOAD_PHOTO = 'upload_photo';
    public const ACTION_UPLOAD_VIDEO = 'upload_video';
    public const ACTION_UPLOAD_VIDEO_NOTE = 'upload_video_note';
    public const ACTION_UPLOAD_VIOCE = 'upload_voice';
    public const ACTION_UPLOAD_DOC = 'upload_document';
    public const ACTION_RECORD_VIDEO = 'record_video';
    public const ACTION_RECORD_VIDEO_NOTE = 'record_video_note';
    public const ACTION_RECORD_VIOCE = 'record_voice';
    public const ACTION_CHOOSE_STICKER = 'choose_sticker';
    public const ACTION_FIND_LOCATION = 'find_location';

    /**
     * Send chat action
     * ارسال حالت چت
     *
     * @param mixed $id
     * @param string $action
     * @return bool
     */
    function action($id, $action='typing'){
        if(gettype($id)=="array")
            return $this->call('sendchataction', $id);
        if(gettype($id)=="object")
            $id = $id->id;
        return $this->call('sendchataction', ['id'=>$id, 'action'=>$action]);
    }
    
    /**
     * Kick chat member
     * حذف ممبر گروه یا کانال
     *
     * @param mixed $chat
     * @param mixed $user
     * @param int $until
     * @return bool
     */
    function kick($chat, $user=null, $until=null){
        if(gettype($chat)=="array")
            return $this->call('banChatMember', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        return $this->call('banChatMember', ['id'=>$chat, 'user'=>$user, 'until'=>$until]);
    }
    /**
     * Ban chat member
     * حذف ممبر گروه یا کانال
     *
     * @param mixed $chat
     * @param mixed $user
     * @param int $until
     * @return bool
     */
    function ban($chat, $user=null, $until=null){
        if(gettype($chat)=="array")
            return $this->call('banchatmember', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        return $this->call('banchatmember', ['id'=>$chat, 'user'=>$user, 'until'=>$until]);
    }
    
    /**
     * Get user profile photos
     * گرفتن تصاویر پروفایل کاربر
     *
     * @param mixed $user
     * @param int $offset
     * @param int $limit
     * @return userProfs|false
     */
    function getUserProfs($user, $offset=null, $limit=null){
        if(gettype($user)=="array"){
            $r = $this->call('getuserprofilephotos', $user);
            if($r)
                return new userProfs($r, $this);
            else
                return false;
        }
        if(gettype($user)=="object")
            $user = $user->id;
        $r = $this->call('getuserprofilephotos', ['user'=>$user, 'offset'=>$offset, 'limit'=>$limit]);
        if($r)
            return new userProfs($r, $this);
        else
            return false;
    }
    
    /**
     * Get file info
     * گرفتن اطلاعات فایل
     *
     * @param string|object $id
     * @return TelFile|false
     */
    function getFile($id){
        if(gettype($id)=="object")
            $id = $id->id;
        if(gettype($id)!="array")
            $id = ['id'=>$id];
        $r = $this->call('getfile', $id);
        if($r)
            return new TelFile($r, $this);
        else
            return false;
    }
    
    /**
     * Unban chat member
     * رفع مسدودیت کاربر در گروه یا کانال
     *
     * @param mixed $chat
     * @param mixed $user
     * @return bool
     */
    function unban($chat, $user=null){
        if(gettype($chat)=="array")
            return $this->call('unbanchatmember', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        return $this->call('unbanchatmember', ['id'=>$chat, 'user'=>$user]);
    }
    
    /**
     * Restrict member
     * محدود کردن کاربر
     *
     * @param mixed $chat
     * @param mixed $user
     * @param array $per
     * @param int $until
     * @return bool
     */
    function restrict($chat, $user=null, $per=null, $until=null){
        if(gettype($chat)=="array")
            return $this->call('restrictchatmember', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        //if(gettype($per)=="object")
        //    $per = $per->getArb();
        return $this->call('restrictchatmember', ['id'=>$chat, 'user'=>$user, 'per'=>$per, 'until'=>$until]);
    }
    
    /**
     * Promote member
     * ترفیع دادن به کاربر
     *
     * @param mixed $chat
     * @param mixed $user
     * @param array $per
     * @return bool
     */
    function promote($chat, $user=null, $per=[]){
        if(gettype($chat)=="array")
            return $this->call('promoteChatMember', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        return $this->call('promoteChatMember', ['id'=>$chat, 'user'=>$user, 'per' => $per]);
    }
    
    /**
     * Set chat permissions
     * تنظیم دسترسی های گروه
     *
     * @param mixed $chat
     * @param array $per
     * @return bool
     */
    function setChatPer($chat, $per=[]){
        if(gettype($chat)=="array")
            return $this->call('setchatpermissions', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('setchatpermissions', ['id' => $chat, 'per' => $per]);
    }
    
    /**
     * Set chat photo
     * تنظیم عکس گروه یا کانال
     *
     * @param mixed $chat
     * @param mixed $photo
     * @return bool
     */
    function setChatPhoto($chat, $photo=null){
        if(gettype($chat)=="array")
            return $this->call('setchatphoto', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($photo)=="array")
            $photo = $photo[0];
        if(gettype($photo)=="object")
            if(isset($photo->id))
                $photo = $photo->id;
        return $this->call('setchatphoto', ['id'=>$chat, 'photo'=>$photo]);
    }
    
    /**
     * Get invite link
     * گرفتن لینک دعوت
     *
     * @param mixed $chat
     * @return string|false
     */
    function getInviteLink($chat){
        if(gettype($chat)=="array")
            return $this->call('exportchatinvitelink', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('exportchatinvitelink', ['id'=>$chat]);
    }

    /**
     * Create invite link
     * ساخت لینک دعوت
     * [chat-name-expire-limit-joinReq]
     *
     * @param array $args
     * @return ChatInvite|false
     */
    function createInviteLink($args){
        $r = $this->call('createchatinvitelink', $args);
        if(!$r)
            return false;
        return new ChatInvite($r, $args['chat'] ?? $args['chat_id'] ?? $args['chatid'] ?? null, $this);
    }

    /**
     * Edit invite link
     * ویرایش لینک دعوت
     * [chat-link-name-expire-limit-joinReq]
     *
     * @param array $args
     * @return ChatInvite|false
     */
    public function editInviteLink($args){
        $r = $this->call('editchatinvitelink', $args);
        if(!$r)
            return false;
        return new ChatInvite($r, $args['chat'] ?? $args['chat_id'] ?? $args['chatid'] ?? null, $this);
    }
    
    /**
     * Remoke invite link
     * منقضی کردن لینک دعوت
     * [chat-link]
     *
     * @param mixed $chat
     * @param string $link
     * @return ChatInvite|false
     */
    public function revokeInviteLink($chat, $link = null){
        if(!is_array($chat))
            $chat = [
                'chat' => $chat
            ];
        if(is_array($link)) {
            foreach($link as $_=>$__)
                $chat[$_] = $__;
        }
        else
            $chat['link'] = $link;

        $r = $this->call('remokechatinvitelink', $chat);
        if(!$r)
            return false;
        return new ChatInvite($r, $chat['chat'] ?? $chat['chat_id'] ?? $chat['chatid'] ?? null, $this);
    }

    /**
     * Approve join request
     * تایید درخواست عضویت توسط لینک
     *
     * @param mixed $chat
     * @param mixed $user
     * @return bool
     */
    public function approveJoinReq($chat, $user = null){
        if(!is_array($chat))
            $chat = [
                'chat' => $chat
            ];
        if(is_array($user)) {
            foreach($user as $_=>$__)
                $chat[$_] = $__;
        }
        else
            $chat['user'] = $user;

        return $this->call('approvechatjoinrequest', $chat);
    }

    /**
     * Decline join request
     * رد کردن درخواست عضویت توسط لینک
     *
     * @param mixed $chat
     * @param mixed $user
     * @return bool
     */
    public function declineJoinReq($chat, $user = null){
        if(!is_array($chat))
            $chat = [
                'chat' => $chat
            ];
        if(is_array($user)) {
            foreach($user as $_=>$__)
                $chat[$_] = $__;
        }
        else
            $chat['user'] = $user;

        return $this->call('declinechatjoinrequest', $chat);
    }

    /**
     * Delete chat photo
     * حذف عکس گروه
     *
     * @param mixed $chat
     * @return bool
     */
    function delChatPhoto($chat){
        if(gettype($chat)=="array")
            return $this->call('deletechatphoto', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('deletechatphoto', ['id'=>$chat]);
    }
    
    /**
     * Set chat title
     * تنظیم عنوان گروه یا کانال
     *
     * @param mixed $chat
     * @param string $title
     * @return bool
     */
    function setChatTitle($chat, $title=""){
        if(gettype($chat)=="array")
            return $this->call('setchattitle', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('setchattitle', ['id'=>$chat, 'title'=>$title]);
    }
    
    /**
     * Set chat description
     * تنظیم توضیحات گروه یا کانال
     *
     * @param mixed $chat
     * @param string $des
     * @return bool
     */
    function setChatDes($chat, $des=""){
        if(gettype($chat)=="array")
            return $this->call('setchatdescription', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('setchatdescription', ['id'=>$chat, 'des'=>$des]);
    }
    
    /**
     * Pin message
     * سنجاق کردن پیام
     *
     * @param mixed $chat
     * @param mixed $msg
     * @return bool
     */
    function pinMsg($chat, $msg=null){
        if(gettype($chat)=="array")
            return $this->call('pinchatmessage', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($msg)=="object")
            $msg = $msg->id;
        return $this->call('pinchatmessage', ['id' => $chat, 'msg' => $msg]);
    }
    
    /**
     * Unpin message
     * برداشتن سنجاق پیام
     *
     * @param mixed $chat
     * @param mixed $msg
     * @return bool
     */
    function unpinMsg($chat, $msg=null){
        if(gettype($chat)=="array")
            return $this->call('unpinchatmessage', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($msg)=="object")
            $msg = $msg->id;
        return $this->call('unpinchatmessage', ['id'=>$chat, 'msg' => $msg]);
    }
    
    /**
     * Unpin all pinned messages
     * برداشتن تمام پیام های سنجاق شده
     *
     * @param mixed $chat
     * @return bool
     */
    function unpinAll($chat){
        if(gettype($chat)=="array")
            return $this->call('unpinchatmessage', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('unpinchatmessage', ['id'=>$chat]);
    }
    
    /**
     * Leave chat
     * ترک گروه یا کانال
     *
     * @param mixed $chat
     * @return bool
     */
    function leave($chat){
        if(gettype($chat)=="array")
            return $this->call('leavechat', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('leavechat', ['id'=>$chat]);
    }
    
    /**
     * Get chat info
     * گرفتن اطلاعات چت
     *
     * @param mixed $chat
     * @return chat|false
     */
    function getChat($chat){
        if(gettype($chat)=="array"){
            $r = $this->call('getchat', $chat);
            if($r)
                return new chat($r, $this);
            else
                return false;
        }
        if(gettype($chat)=="object")
            $chat = $chat->id;
        $r = $this->call('getchat', ['id'=>$chat]);
        if($r)
            return new chat($r, $this);
        else
            return false;
    }
    
    /**
     * Get chat admins list
     * گرفتن لیست ادمین ها
     *
     * @param mixed $chat
     * @return chatMember[]|false
     */
    function getChatAdmins($chat){
        if(gettype($chat)=="array"){
            $r = $this->call('getchatadministrators', $chat);
        }else{
            if(gettype($chat)=="object")
                $chat = $chat->id;
            $r = $this->call('getchatadministrators', ['id'=>$chat]);
        }
        if(!$r) return false;
        $ar=[];
        foreach($r as $one)
            $ar[] = new chatMember($one, $this);
        return $ar;
    }
    
    /**
     * Get chat members count
     * گرفتن تعداد اعضای چت
     *
     * @param mixed $chat
     * @return int|false
     */
    function getChatMemberNum($chat){
        if(gettype($chat)=="array")
            return $this->call('getchatmembercount', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('getchatmembercount', ['id'=>$chat]);
    }
    
    /**
     * Get chat members count
     * گرفتن تعداد اعضای چت
     *
     * @param mixed $chat
     * @return int|false
     */
    function getChatMemberCount($chat){
        return $this->getChatMemberNum($chat);
    }
    
    /**
     * Get chat member
     * گرفتن اطلاعات یک کاربر در چت
     *
     * @param mixed $chat
     * @param mixed $user
     * @return chatMember|false
     */
    function getChatMember($chat, $user=null){
        if(gettype($chat)=="array"){
            $r = $this->call('getchatmember', $chat);
            if($r)
                return new chatMember($r, $this);
            else
                return false;
        }
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($user)=="object")
            $user = $user->id;
        $r = $this->call('getchatmember', ['id'=>$chat, 'user'=>$user]);
        if($r)
            return new chatMember($r, $this);
        else
            return false;
    }
    
    function SetChatStickerSet($chat, $setName=null){
        if(gettype($chat)=="array")
            return $this->call('setchatstickerset', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        if(gettype($setName)=="object")
            $setName = $setName->setName;
        return $this->call('setchatstickerset', ['id'=>$chat, 'setName'=>$setName]);
    }
    
    function delChatStickerSet($chat){
        if(gettype($chat)=="array")
            return $this->call('deletechatstickerset', $chat);
        if(gettype($chat)=="object")
            $chat = $chat->id;
        return $this->call('deletechatstickerset', ['id'=>$chat]);
    }
    
    function setMyCmds($cmds){
        if(isset($cmds['cmds']))
            $cmds = $cmds['cmds'];
        $c=[];
        foreach($cmds as $cm){
            if(gettype($cm)=="object")
                $cm = $cm->toAr();
            else
                $cm = filterArray($cm, ['cmd'=>"command", 'des'=>"description"]);
            $c[] = $cm;
        }
        $cmds = $c;
        return $this->call('setmycommands', ['cmds'=>$cmds]);
    }
    
    function getMyCmds(array $args = []){
        $b = $this->call('getmycommands', $args);
        $r=[];
        foreach($b as $a)
            $r[] = new botCmd($a, $this);
        return $r;
    }
    
    /**
     * Answer inline query
     * پاسخ به اینلاین کوئری
     * 
     * @param array $args
     * @return bool
     */
    function answerInline($args){
        return $this->call('answerinlinequery', $args);
    }
    
    /**
     * Edit message text
     * ویرایش متن پیام
     *
     * @param array $args
     * @return msg|false
     */
    function editMsgText($args){
        $r = $this->call('editmessagetext', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Edit message caption
     * ویرایش توضیحات زیر پیام
     *
     * @param array $args
     * @return msg|false
     */
    function editMsgCaption($args){
        if(isset($args['text'])){
            $args['caption'] = $args['text'];
            unset($args['text']);
        }
        $r = $this->call('editmessagecaption', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Edit message media
     * ویرایش رسانه پیام
     *
     * @param array $args
     * @return msg|false
     */
    function editMsgMedia($args){
        $r = $this->call('editmessagemedia', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    /**
     * Edit message keyboard(inline keyboard)
     * ویرایش دکمه های پیام(دکمه های شیشه ای)
     *
     * @param array $args
     * @return msg|false
     */
    function editMsgKey($args){
        $r = $this->call('editmessagereplymarkup', $args);
        if($r)
            return new msg($r, $this);
        else
            return false;
    }
    
    function getStickerSet($setName){
        if(gettype($setName)=="array")
            $setName = array_values($setName)[0];
        if(gettype($setName)=="object")
            $setName = $setName->setName;
        return new stickerSet($this->call('getstickerset', $setName), $this);
    }
    
    function copyByFilePath($path, $paste){
        return copy("https://api.telegram.org/file/bot" . $this->_token . "/" . $path, $paste);
    }

    /**
     * Set webhook
     * تنظیم وبهوک
     *
     * @param array $url
     * @param bool $drop Drop pending updates
     * @return bool
     */
    function setWebhook($url, $drop = null){
        if(gettype($url) != "array")
            $url = ['url' => $url, 'drop' => $drop];
        return $this->call('setwebhook', $url);
    }

    /**
     * Get webhook info
     * گرفتن اطلاعات وبهوک
     *
     * @param array $args
     * @return WebhookInfo|false
     */
    function getWebhook(array $args = []){
        $r = $this->call('getwebhookinfo', $args);
        if(!$r)
            return false;
        return new WebhookInfo($r, $this);
    }

    /**
     * Merge 2 array, but only can use first array keys
     * الحاق دو آرایه، با این تفاوت که تنها کلید های آرایه اول قابل استفاده است
     *
     * @param array $default_settings
     * @param array $input_settings
     * @return array
     */
    static function pluginFixSettings(array $default_settings, array $input_settings){
        foreach($input_settings as $k => $v){
            if(isset($default_settings[$k])){
                $default_settings[$k] = $v;
            }
        }
        return $default_settings;
    }

    public function serialize()
    {
        return "[Mmb]";
    }

    public function unserialize($serialized)
    {
        $this->_token = self::$this->_token;
    }
}

class BotCmd extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;
    function __construct($b, $base){
        $this->_base = $base;
        $this->cmd = $b['command'];
        $this->des = $b['description'];
    }
    
    function toAr(){
        return ['command'=>$this->cmd, 'description'=>$this->des];
    }
}

class WebhookInfo extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * Webhook url
     *
     * @var string
     */
    public $url;

    /**
     * Pending update count
     * تعداد آپدیت های درون صف
     *
     * @var int
     */
    public $pendings;

    /**
     * آی پی تنظیم شده
     *
     * @var string
     */
    public $ip;

    /**
     * Last error time
     * تاریخ آخرین خطا
     *
     * @var int
     */
    public $lastErrorTime;

    /**
     * Last error message
     * آخرین خطا
     *
     * @var string
     */
    public $lastError;

    /**
     * Max connections
     *
     * @var int
     */
    public $maxConnections;

    /**
     * Allowed updates
     *
     * @var string[]
     */
    public $allowedUpds;

    function __construct($data, $base){
        $this->_base = $base;
        $this->url = $data['url'];
        $this->pendings = $data['pending_update_count'];
        $this->ip = $data['ip_address'];
        $this->lastErrorTime = $data['last_error_date'];
        $this->lastError = $data['last_error_message'];
        $this->maxConnections = $data['max_connections'];
        $this->allowedUpds = $data['allowed_updates'];
    }
}

// Copyright (C): t.me/MMBlib

if (PHP_MAJOR_VERSION === 5 || (PHP_MAJOR_VERSION === 7 && PHP_MINOR_VERSION === 0)) {
    echo "نسخه پی اچ پی شما پشتیبانی نمی شود! لطفا از نسخه های 7.1 به بالا استفاده کنید";
    mmb_error_throw("Your php version is not supported! Change it to 7.1+");
}

set_exception_handler(function($exception){
    $trace = $exception->getTrace();
    $trace2 = explode("\n", $exception->getTraceAsString());
    $trace_str = "";
    foreach($trace as $i => $t){
        #if(substr($t['file'], -7) == 'Mmb.php')
        #    continue;
        $file = $t['file'];
        $line = $t['line'];
        $text = str_replace(["#$i ", "$file($line): ", "$file"], '', $trace2[$i]);
        $trace_str .= "\n    On $text\n        File: $file\tLine: $line";
    }
    $error = $exception->getMessage();
    mmb_log("You have a unhandled exception: $error$trace_str");
});

// Copyright (C): t.me/MMBlib

// Plugins
$plugins = @scandir(PLUGINS) ?: [];
foreach($plugins as $file){
    $ext = explode('.', $file);
    $ext = end($ext);
    if($ext == "php"){
        require_once PLUGINS . '/' . $file;
    }
}
unset($ext, $plugins);

Listeners::__runReady();
<?php

// Copyright (C): t.me/MMBlib

class Msg extends MmbBase implements IMsg, IUser, IChat{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * مقدار های قابل قبول کد استارت(به صورت کد ریجکس)
     * 
     * @var string $acceptStartCode
     */
    public static $acceptStartCode = '\d\w\-_';
    /**
     * @var Mmb
     */
    private $_base;
    /**
     * Message id
     * آیدی عددی پیام
     *
     * @var int|null
     */
    public $id;
    /**
     * Is inline message
     * آیا پیام مربوط به حالت اینلاین است
     *
     * @var bool
     */
    public $isInline;
    /**
     * Message id for inline mode
     * شناسه پیام برای حالت اینلااین
     *
     * @var bool
     */
    public $inlineID;
    /**
     * Is started bot?
     * آیا ربات استارت شده؟
     *
     * @var bool
     */
    public $started;
    /**
     * Start code
     * کد استارت
     * 
     * /start [CODE]
     *
     * @var string|null
     */
    public $startCode = null;
    /**
     * اگر پیام کاربر با / شروع شود، متن دستور را به شما می دهد
     *
     * @var string|null
     */
    public $command = null;
    private $commandLower = null;
    /**
     * اگر پیام کاربر با / شروع شود، متن مقابل دستور را به شما می دهد
     *
     * @var string|null
     */
    public $commandData = null;
    /**
     * اگر پیام کاربر با / شروع شود، آیدی همراه با @ جلوی دستور را میدهد
     *
     * @var string|null
     */
    public $commandTag = null;
    /**
     * Text or caption of message
     * متن یا عنوان پیام
     *
     * @var string|null
     */
    public $text;
    /**
     * Type of message
     * نوع پیام
     *
     * @var string|null
     */
    public $type;
    /**
     * MMB media (photo|doc|voice|video|anim|audio)
     * رسانه های ام.ام.بی (عکس، مستند، ویس، فیلم، گیف، صدا)
     *
     * @var MsgData|null
     */
    public $media;
    /**
     * MMB media id
     * آیدی رسانه های ام.ام.بی
     *
     * @var string
     */
    public $media_id;
    /**
     * Photo
     * تصویر
     *
     * @var MsgData[]|null
     */
    public $photo;
    /**
     * Document
     * مستند
     *
     * @var MsgData|null
     */
    public $doc;
    /**
     * Voice
     * صدا
     *
     * @var MsgData|null
     */
    public $voice;
    /**
     * Video
     * فیلم
     *
     * @var MsgData|null
     */
    public $video;
    /**
     * Animation (GIF)
     * گیف
     *
     * @var MsgData|null
     */
    public $anim;
    /**
     * Audio
     * صوت
     *
     * @var MsgData|null
     */
    public $audio;
    /**
     * Video note
     * ویدیو سلفی
     *
     * @var MsgData|null
     */
    public $videoNote;
    /**
     * Location
     * مختصات
     *
     * @var Location|null
     */
    public $location;
    /**
     * Dice
     * شانس
     *
     * @var Dice|null
     */
    public $dice;
    /**
     * Poll
     * نظرسنجی
     *
     * @var Poll|null
     */
    public $poll;
    /**
     * Contact
     * مخاطب
     *
     * @var Contact|null
     */
    public $contact;
    /**
     * Sticker
     * استیکر
     *
     * @var Sticker|null
     */
    public $sticker;
    /**
     * New members
     * عضو های جدید
     *
     * @var User[]|null
     */
    public $newMembers;
    /**
     * Left member
     * عضو ترک شده
     *
     * @var User|null
     */
    public $leftMember;
    /**
     * New title
     * عنوان جدید
     *
     * @var string
     */
    public $newTitle;
    /**
     * New photo
     * تصویر پروفایل جدید
     *
     * @var MsgData[]
     */
    public $newPhoto;
    /**
     * Delete photo
     * حذف تصویر پروفایل
     *
     * @var bool
     */
    public $delPhoto;
    /**
     * New group
     * گروه جدید
     *
     * @var bool
     */
    public $newGroup;
    /**
     * New super group
     * سوپر گروه جدید
     *
     * @var bool
     */
    public $newSupergroup;
    /**
     * New channel
     * کانال جدید
     *
     * @var bool
     */
    public $newChannel;
    /**
     * Reply to message
     * پیام ریپلای شده
     *
     * @var Msg|null
     */
    public $reply;
    /**
     * Chat info
     * اطلاعات چت
     *
     * @var Chat|null
     */
    public $chat;
    /**
     * Sender chat info
     * چت ارسال کننده
     *
     * @var Chat|null
     */
    public $sender;
    /**
     * User info
     * اطلاعات ارسال کننده
     *
     * @var User|null
     */
    public $from;
    /**
     * Message date
     * تاریخ ارسال پیام
     *
     * @var int|null
     */
    public $date;
    /**
     * Media group id
     * آیدی آلبوم
     *
     * @var string
     */
    public $mediaGroupID;
    /**
     * Is edited?
     * ویرایش شده؟
     *
     * @var bool
     */
    public $edited;
    /**
     * Edit date
     * تاریخ ویرایش پیام
     *
     * @var int|null
     */
    public $editDate;
    /**
     * Is forwarded?
     * باز ارسال شده؟
     *
     * @var bool
     */
    public $forwarded;
    /**
     * Forward from user (if forwarded from a user)
     * کاربری که پیام آن باز ارسال شده است (در صورت باز ارسال از کاربر)
     *
     * @var User|null
     */
    public $forwardFrom;
    /**
     * Forward from chat (if forwarded from a chat)
     * چتی که پیام از آنجا باز ارسال شده است (در صورت باز ارسال از چت)
     *
     * @var Chat|null
     */
    public $forwardChat;
    /**
     * Forward from message id (if forwarded from a chat)
     * آیدی پیام در چت باز ارسال شده (در صورت باز ارسال از چت)
     *
     * @var int|null
     */
    public $forwardMsgId;
    /**
     * Forward from message signature (if forwarded from a chat and message has signature)
     * امضای پیام (در صورت باز ارسال از چت و داشتن امضا)
     *
     * @var string|null
     */
    public $forwardSig;
    /**
     * Forward date
     * تاریخ پیام باز ارسال شده
     *
     * @var int|null
     */
    public $forwardDate;
    /**
     * Entities
     * نهاد ها(علائمی همچون لینک، تگ، منشن و ...)
     *
     * @var Entity[]|null
     */
    public $entities;
    /**
     * Pinned message
     * پیام سنجاق شده
     *
     * @var Msg|null
     */
    public $pinnedMsg;
    /**
     * Message keyboard
     * دکمه های پیام
     *
     * @var array|null
     */
    public $key;
    /**
     * Via bot
     * رباتی که پیغام توسط آن ایجاد شده
     *
     * @var User|null
     */
    public $via;
    /**
     * User in chat object
     * کاربر در چت
     *
     * @var UserInChat|null
     */
    public $userInChat;

    public const TYPE_TEXT = 'text';
    public const TYPE_PHOTO = 'photo';
    public const TYPE_VOICE = 'voice';
    public const TYPE_VIDEO = 'video';
    public const TYPE_ANIM = 'anim';
    public const TYPE_AUDIO = 'audio';
    public const TYPE_VIDEO_NOTE = 'video_note';
    public const TYPE_LOCATION = 'location';
    public const TYPE_DICE = 'dice';
    public const TYPE_STICKER = 'sticker';
    public const TYPE_CONTACT = 'contact';
    public const TYPE_DOC = 'doc';
    public const TYPE_POLL = 'poll';
    
    public const TYPE_NEW_MEMBERS = 'new_members';
    public const TYPE_LEFT_MEMBER = 'left_member';
    public const TYPE_NEW_TITLE = 'new_title';
    public const TYPE_NEW_PHOTO = 'new_photo';
    public const TYPE_DEL_PHOTO = 'del_photo';
    public const TYPE_NEW_GROUP = 'new_group';
    public const TYPE_NEW_SUPERGROUP = 'new_supergroup';
    public const TYPE_NEW_CHANNEL = 'new_channel';

    function __construct($msg, $base, $isInline = false){
        $this->_base = $base;

        if($isInline){
            $this->isInline = true;
            $this->inlineID = $msg;
            return;
        }
        $this->isInline = false;

        $this->id = $msg['message_id'];
        $this->started = false;
        if(isset($msg['text'])){
            $this->text = $msg['text'];
            $this->type = "text";
            if(@$this->text[0] == "/"){
                if($this->started = preg_match('/^\/start(\s(['.(self::$acceptStartCode).']+)|)$/i', $this->text, $r))
                    $this->startCode = @$r[2];
                if(preg_match('/^\/([a-zA-Z0-9_]+)(@[a-zA-Z0-9_]+|)/', $this->text, $r)){
                    $this->command = $r[1];
                    $this->commandTag = $r[2];
                    $this->commandData = ltrim(substr($this->text, strlen($r[0])));
                }
            }
        }elseif(isset($msg['caption'])){
            $this->text = $msg['caption'];
        }
        if(isset($msg['photo'])){
            $this->type = "photo";
            $this->photo = [];
            foreach($msg['photo'] as $a){
                $this->photo[] = new msgData("photo", $a, $base);
            }
            $this->media = end($this->photo);
            $this->media_id = $this->media->id;
        }elseif(isset($msg['voice'])){
            $this->type = "voice";
            $this->voice = new msgData("voice", $msg['voice'], $base);
        }elseif(isset($msg['video'])){
            $this->type = "video";
            $this->media = new msgData("video", $msg['video'], $base);
            $this->media_id = $this->media->id;
            $this->video = $this->media;
        }elseif(isset($msg['animation'])){
            $this->type = "anim";
            $this->media = new msgData("anim", $msg['animation'], $base);
            $this->media_id = $this->media->id;
            $this->anim = $this->media;
        }elseif(isset($msg['audio'])){
            $this->type = "audio";
            $this->media = new msgData("audio", $msg['audio'], $base);
            $this->media_id = $this->media->id;
            $this->audio = $this->media;
        }elseif(isset($msg['video_note'])){
            $this->type = "video_note";
            $this->videoNote = new msgData("videonote", $msg['video_note'], $base);
        }
        elseif(isset($msg['location'])){
            $this->type = "location";
            $this->location = new location($msg['location'], $base);
        }
        elseif(isset($msg['dice'])){
            $this->type = "dice";
            $this->dice = new dice($msg['dice'], $base);
        }
        elseif(isset($msg['poll'])){
            $this->type = "poll";
            $this->poll = new Poll($msg['poll'], $base);
        }
        elseif(isset($msg['sticker'])){
            $this->type = "sticker";
            $this->sticker = new sticker($msg['sticker'], $base);
        }
        elseif(isset($msg['contact'])){
            $this->type = "contact";
            $this->contact = new contact($msg['contact'], $base);
        }
        elseif(isset($msg['new_chat_members'])){
            $this->type = "new_members";
            $this->newMembers = [];
            foreach($msg['new_chat_members']as$once)
                $this->newMembers[] = new user($once, $base);
        }
        elseif(isset($msg['left_chat_member'])){
            $this->type = "left_member";
            $this->leftMember = new user($msg['left_chat_member'], $base);
        }
        elseif(isset($msg['new_chat_title'])){
            $this->type = "new_title";
            $this->newTitle = $msg['new_chat_title'];
        }
        elseif(isset($msg['new_chat_photo'])){
            $this->type = "new_photo";
            $this->newPhoto = [];
            foreach($msg['new_chat_photo'] as $once)
                $this->newPhoto[] = new msgData("photo", $once, $base);
        }
        elseif(isset($msg['delete_chat_photo'])){
            $this->type = "del_photo";
            $this->delPhoto = true;
        }
        elseif(isset($msg['group_chat_created'])){
            $this->type = "new_group";
            $this->newGroup = true;
        }
        elseif(isset($msg['supergroup_chat_created'])){
            $this->type = "new_supergroup";
            $this->newSupergroup = true;
        }
        elseif(isset($msg['channel_chat_created'])){
            $this->type = "new_channel";
            $this->newChannel = true;
        }
        if(isset($msg['document'])){
            if(!$this->type){
                $this->type = "doc";
            }
            if(!$this->media){
                $this->media = new msgData("doc", $msg['document'], $base);
                $this->media_id = $this->media->id;
            }
            $this->doc = $this->media;
        }
        if(isset($msg['reply_to_message'])){
            $this->reply = new msg($msg['reply_to_message'], $base);
        }
        if(isset($msg['chat'])){
            $this->chat = new chat($msg['chat'], $base);
        }
        if(isset($msg['from'])){
            $this->from = new user($msg['from'], $base);
        }
        $this->date = @$msg['date'];
        $this->edited = isset($msg['edit_date']);
        if($this->edited) $this->editDate = $msg['edit_date'];
        if(isset($msg['forward_from'])){
            $this->forwarded = true;
            $this->forwardFrom = new user($msg['forward_from'], $base);
        }
        elseif(isset($msg['forward_from_chat'])){
            $this->forwarded = true;
            $this->forwardChat = new chat($msg['forward_from_chat'], $base);
            $this->forwardMsgId = $msg['forward_from_message_id'];
            $this->forwardSig = @$msg['forward_signature'];
        }
        else{
            $this->forwarded = false;
        }
        if($this->forwarded)
            $this->forwardDate = @$msg['forward_date'];
        if(isset($msg['entities']))
            $e = $msg['entities'];
        elseif(isset($msg['caption_entities']))
            $e = $msg['caption_entities'];
        else
            $e = [];
        $this->entities = [];
        foreach($e as $once)
            $this->entities[] = new entity($once, $base);
        if(isset($msg['pinned_message'])){
            $this->pinnedMsg = new msg($msg['pinned_message'], $base);
        }
        if(isset($msg['reply_markup'])){
            try{
                $this->key = filterArray3D($msg['reply_markup'], ['text'=>"text", 'callback_data'=>"data", 'url'=>"url", 'login_url'=>"login"],null);
            }catch(Exception $e){
                $this->key = null;
            }
        }
        if($this->chat && $this->from && $this->chat->id != $this->from->id){
            $this->userInChat = new userInChat($this->from, $this->chat, $base);
        }
        if($_ = @$msg['via_bot'])
            $this->via = new user($_, $base);
        if($_ = @$msg['sender_chat'])
            $this->sender = new Chat($_, $this);
        if($_ = @$msg['media_group_id'])
            $this->mediaGroupID = $_;
    }
    
    /**
     * Reply to message and send text message
     * پاسخ به پیام با ارسال متن
     *
     * @param string|array $text
     * @param array $args
     * @return msg
     */
    function replyText($text, $args=[]){
        if(gettype($text) == "array"){
            $args = array_merge($text, $args);
        }else{
            $args['text'] = $text;
        }
        $args['id'] = $this->chat->id;
        $args['reply'] = $this->id;
        return $this->_base->sendMsg($args);
    }
    
    /**
     * Reply to message and send x message
     * پاسخ به پیام با ارسال پیامی با نوع x
     *
     * @param string|array $type
     * @param array $args
     * @return msg|false
     */
    function reply($type, $args=[]){
        if(gettype($type) == "array"){
            $args = array_merge($type, $args);
            $type = @$args['type'];
            unset($args['type']);
        }
        $args['id'] = $this->chat->id;
        $args['reply'] = $this->id;
        return $this->_base->send($type, $args);
    }

    /**
     * Send message
     * ارسال پیام
     *
     * @param string|array $text
     * @param array $args
     * @return msg|false
     */
    function sendMsg($text, $args = []){
        if(gettype($text) == "array"){
            $args = array_merge($text, $args);
        }else{
            $args['text'] = $text;
        }
        $args['id'] = $this->chat->id;
        return $this->_base->sendMsg($args);
    }
    
    /**
     * Send x message
     * ارسال پیام با ارسال پیامی با نوع x
     *
     * @param string|array $type
     * @param array $args
     * @return msg|false
     */
    function send($type, $args=[]){
        if(gettype($type) == "array"){
            $args = array_merge($type, $args);
            $type = @$args['type'];
            unset($args['type']);
        }
        $args['id'] = $this->chat->id;
        return $this->_base->send($type, $args);
    }

    /**
     * Delete message
     * حذف پیام
     *
     * @return bool
     */
    function del(){
        return $this->_base->call('deletemessage', ['id' => $this->chat->id, 'msg' => $this->id]);
    }
    
    /*function edit($text, $media=null, $args=[]){
        if($this->type == "text"){
            $args = array_merge($media, $args);
            return new msg($this->_base->call('editmessagetext', array_merge(['id'=>$this->chat->id, 'msg'=>$this->id, 'text'=>$text], $args)), $this->_base);
        }else{
            
        }
    }*/
    
    /**
     * Edit message text
     * ویرایش متن پیام
     *
     * @param string|array $text
     * @param array $args
     * @return Msg|false
     */
    function editText($text, $args=[]){
        if(gettype($text)=="array")
            $args = array_merge($args, $text);
        else
            $args['text'] = $text;

        if($this->isInline){
            $args['inlineMsg'] = $this->inlineID;
        }
        else{
            $args['id'] = $this->chat->id;
            $args['msg'] = $this->id;
        }

        if($this->type == "text" || !$this->type){
            return $this->_base->editMsgText($args);
        }else{
            return $this->_base->editMsgCaption($args);
        }
    }

    /**
     * Edit message caption
     * ویرایش عنوان پیام
     *
     * @param string|array $text
     * @param array $args
     * @return Msg|false
     */
    function editCaption($text, $args=[]){
        if(gettype($text)=="array")
            $args = array_merge($args, $text);
        else
            $args['text'] = $text;

        if($this->isInline){
            $args['inlineMsg'] = $this->inlineID;
        }
        else{
            $args['id'] = $this->chat->id;
            $args['msg'] = $this->id;
        }

        return $this->_base->editMsgCaption($args);
    }
    
    /**
     * Edit message keyboard
     * ویرایش دکمه های پیام
     *
     * @param array $newKey
     * @return Msg|false
     */
    function editKey($newKey){
        $ar = [
            'key' => $newKey
        ];
        if($this->isInline){
            $ar['inlineMsg'] = $this->inlineID;
        }
        else{
            $ar['id'] = $this->chat->id;
            $ar['msg'] = $this->id;
        }
        $r = $this->_base->call('editmessagereplymarkup', $ar);
        if($r)
            return new msg($r, $this->_base);
        else
            return false;
    }
    
    /**
     * Forward message
     * باز ارسال پیام
     *
     * @param mixed $id Chat id
     * @return Msg|false
     */
    function forward($id){
        $r = $this->_base->call("forwardmessage", ['id'=>$id, 'msg'=>$this->id, 'from'=>$this->chat->id]);
        if($r)
            return new msg($r, $this->_base);
        else
            return false;
    }
    /**
     * Forward message
     * باز ارسال پیام
     *
     * @param mixed|array $id Chat id
     * @return Msg|false
     */
    function forwardTo($id){
        $args = is_array($id) ? $id : ['chat' => $id];
        $args['msg'] = $this->id;
        $args['from'] = $this->chat->id;
        $r = $this->_base->call("forwardmessage", $args);
        if($r)
            return new msg($r, $this->_base);
        else
            return false;
    }

    /**
     * Forward message without linking to original message
     * باز ارسال پیام بدون نام
     *
     * @param mixed|array $id Chat id
     * @return Msg|false
     */
    function copyTo($id){
        $args = is_array($id) ? $id : ['chat' => $id];
        $args['msg'] = $this->id;
        $args['from'] = $this->chat->id;
        return $this->_base->copyMsg($args);
    }

    /**
     * Pin message in chat
     * پین کردن پیام در چت
     *
     * @return bool
     */
    function pin(){
        return $this->_base->pinMsg(['chat' => $this->chat->id, 'msg'=>$this->id]);
    }

    /**
     * Unpin message from chat
     * برداشتن پین پیام از چت
     *
     * @return bool
     */
    function unpin(){
        return $this->_base->unpinMsg(['chat' => $this->chat->id, 'msg'=>$this->id]);
    }
    
    public function __getChatID()
    {
        return $this->chat->id;
    }

    public function __getUserID()
    {
        return $this->from->id;
    }

    public function __getMsgID()
    {
        return $this->id;
    }

    /**
     * ساخت ورودی از متن و محتویات پیام
     *
     * @return array|false در صورت ناموفق بودن فالس را برمیگرداند
     */
    public function createArgs(){
        if($this->type == self::TYPE_TEXT){
            return [
                'type' => 'text',
                'text' => $this->text
            ];
        }
        if($this->media){
            return [
                'type' => $this->type,
                $this->type => $this->media_id,
                'text' => $this->text,
            ];
        }
        return false;
    }

    /**
     * بررسی می کند آیا این پیام با این دستور است
     * 
     * اگر پیامی با / شروع شود، متن جلوی اسلش نام دستور است
     * 
     * @param string $command نام دستور
     * @param boolean $ignoreCase صرف نظر از بزرگی و کوچکی حروف
     * @return boolean
     */
    public function isCommand(string $command, bool $ignoreCase = true){
        if($this->command === null) return false;
        if($ignoreCase){
            if($this->commandLower === null) $this->commandLower = strtolower($this->command);
            return strtolower($command) == $this->commandLower;
        }
        else{
            return $command == $this->command;
        }
    }
    
}

class Dice extends MmbBase{
    /**
     * Dice emoji
     * اموجی
     *
     * @var string
     */
    public $emoji;
    /**
     * Dice value
     * مفدار
     *
     * @var int
     */
    public $val;
    /**
     * @var MMB
     */
    private $_base;
    public function __construct($data, $base){
        $this->_base = $base;
        $this->emoji = $data['emoji'];
        $this->val = $data['value'];
    }
}

class ICMsgData extends MmbBase implements IMsgData{

    public function __getMsgDataID()
    {
        return $this->id ?? null;
    }

}

class MsgData extends ICMsgData{
    /**
     * @var Mmb
     */
    private $_base;
    /**
     * File id
     * آیدی فایل
     *
     * @var string
     */
    public $id;
    /**
     * File unique id
     * آیدی یکتای فایل
     *
     * @var string
     */
    public $uniqueID;
    /**
     * File size
     * حجم فایل
     *
     * @var int|null
     */
    public $size;
    /**
     * File name
     * اسم فایل
     *
     * @var string|null
     */
    public $name;
    /**
     * Thump
     *
     * @var MsgData|null
     */
    public $thumb;
    /**
     * Mime type
     * مایم تایپ
     *
     * @var string|null
     */
    public $mime;
    /**
     * Duration (for audios, videos and ...)
     * طول رسانه(برای صوت، ویدیو، ...)
     *
     * @var int|null
     */
    public $duration;
    /**
     * Photo, video or animation width
     * عرض عکس، ویدیو یا گیف
     *
     * @var int|null
     */
    public $width;
    /**
     * Photo, video or animation height
     * ارتفاع عکس، ویدیو یا گیف
     *
     * @var int|null
     */
    public $height;
    /**
     * Audio perfomer
     * ایفا کننده ی صوت
     *
     * @var string|null
     */
    public $perfomer;
    /**
     * Audio title
     * نام صوت
     *
     * @var string|null
     */
    public $title;
    /**
     * File extension
     * پسوند فایل
     *
     * @var string|null
     */
    public $ext;
    function __construct($type, $med, $base){
        $this->_base = $base;
        $this->id = $med['file_id'];
        $this->uniqueID = @$med['file_unique_id'];
        $this->size = @$med['file_size'];
        $this->name = @$med['file_name'];
        if($ext = $this->name){
            $ext = explode('.', $ext);
            $ext = end($ext);
            $this->ext = $ext;
        }
        if(isset($med['thumb']))
            $this->thumb = new msgData("photo", $med['thumb'], $base);
        if(isset($med['mime_type']))
            $this->mime = $med['mime_type'];
        if(isset($med['duration']))
            $this->duration = $med['duration'];
        if($type == "photo"){
            $this->width = $med['width'];
            $this->height = $med['height'];
        }
        elseif($type == "audio"){
            $this->perfomer = @$med['permofer'];
            $this->title = @$med['title'];
        }
        elseif($type == "video"){
            $this->width = $med['width'];
            $this->height = $med['height'];
        }
        elseif($type == "anim"){
            $this->width = $med['width'];
            $this->height = $med['height'];
        }
        elseif($type == "videoNote"){
            $this->duration = $med['length'];
        }
    }
    
    /**
     * Downlaod file
     * دانلود کردن فایل
     *
     * @param string $path Download file to ...
     * @return bool
     */
    function download($path){
        return $this->_base->getFile($this->id)->download($path);
    }

    /**
     * Get file
     * دریافت اطلاعات فایل
     *
     * @return TelFile|false
     */
    public function getFile(){
        return $this->_base->getFile($this->id);
    }
}

class Sticker extends ICMsgData{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * File id
     * شناسه فایل
     *
     * @var string
     */
    public $id;
    /**
     * File unique id
     * شناسه یکتای فایل
     *
     * @var string
     */
    public $uniqueID;
    /**
     * Width
     * عرض
     *
     * @var int
     */
    public $width;
    /**
     * Height
     * ارتفاع
     *
     * @var int
     */
    public $height;
    /**
     * Is animated
     * آیا متحرک است
     *
     * @var bool
     */
    public $isAnim;
    /**
     * Thumb
     * تصویر کوچک
     *
     * @var MsgData
     */
    public $thumb;
    /**
     * Set name
     * نام بسته استیکر
     *
     * @var string
     */
    public $setName;
    /**
     * Mask position
     *
     * @var MaskPos
     */
    public $maskPos;
    /**
     * File size in bytes
     * حجم فایل به بایت
     *
     * @var int
     */
    public $size;
    function __construct($st, $base){
        $this->_base = $base;
        $this->id = $st['file_id'];
        $this->uniqueID = $st['file_unique_id'];
        $this->width = $st['width'];
        $this->height = $st['height'];
        $this->isAnim = $st['is_animated'];
        if(isset($st['thumb']))
            $this->thumb = new MsgData("photo", $st['thumb'], $base);
        $this->emoji = @$st['emoji'];
        $this->setName = @$st['set_name'];
        if(isset($st['mask_position']))
            $this->maskPos = new MaskPos(@$st['mask_position'], $base);
        $this->size = @$st['file_size'];
    }

    /**
     * Downlaod file
     * دانلود کردن فایل
     *
     * @param string $path Download file to ...
     * @return bool
     */
    function download($path){
        return $this->_base->getFile($this->id)->download($path);
    }

    /**
     * Get file
     * دریافت اطلاعات فایل
     *
     * @return StickerSet|false
     */
    public function getFile(){
        return $this->_base->getFile($this->id);
    }

    /**
     * Get sticker set
     * دریافت اطلاعات بسته استیکر
     *
     * @return StickerSet|false
     */
    public function getSet(){
        return $this->_base->getStickerSet($this->setName);
    }
}

class MaskPos extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * Point
     * موقعیت
     *
     * @var string
     */
    public $point;
    public const POINT_FOREHEAD = 'forehead';
    public const POINT_EYES = 'eyes';
    public const POINT_MOUTH = 'mouth';
    public const POINT_CHIN = 'chin';
    /**
     * X
     *
     * @var double
     */
    public $x;
    /**
     * Y
     *
     * @var double
     */
    public $y;
    /**
     * Scale
     *
     * @var double
     */
    public $scale;
    public function __construct($a, $base)
    {
        $this->_base = $base;
        $this->point = $a['point'];
        $this->x = $a['x_shift'];
        $this->y = $a['y_shift'];
        $this->scale = $a['scale'];
    }
}

class StickerSet extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * Set name
     * نام
     *
     * @var string
     */
    public $name;
    /**
     * Set title
     * عنوان
     *
     * @var string
     */
    public $title;
    /**
     * Contains animated sticker
     * آیاا استیکر متحرک دارد
     *
     * @var bool
     */
    public $hasAnim;
    /**
     * Contains mask sticker
     * آیا استیکر ماسک دارد
     *
     * @var bool
     */
    public $hasMask;
    /**
     * Pack stickers
     * استیکر ها
     *
     * @var Sticker[]
     */
    public $stickers;
    /**
     * Thumb
     * عکس کوچک
     *
     * @var MsgData
     */
    public $thumb;
    function __construct($a, $base){
        $this->_base = $base;
        $this->name = $a['name'];
        $this->title = $a['title'];
        $this->hasAnim = $a['is_animated'];
        $this->hasMask = $a['contains_masks'];
        $this->stickers = [];
        foreach($a['stickers'] as $once)
            $this->stickers[] = new Sticker($once, $base);
        if(isset($a['thumb']))
            $this->thumb = new MsgData("photo", $a['thumb'], $base);
    }
}

class Contact extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;

    /**
     * Contact number
     * شماره کاربر
     *
     * @var string
     */
    public $num;
    /**
     * First name
     * نام کوچک مخاطب
     *
     * @var string
     */
    public $firstName;
    /**
     * Last name
     * نام بزرگ مخاطب
     *
     * @var string
     */
    public $lastName;
    /**
     * Full name
     * نام کامل مخاطب
     *
     * @var string
     */
    public $name;
    /**
     * User id
     * ایدی عددی صاحب مخاطب
     *
     * @var int
     */
    public $userID;
    function __construct($con, $base){
        $this->_base = $base;
        $this->num = $con['phone_number'];
        $this->firstName = @$con['first_name'];
        $this->lastName = @$con['last_name'];
        $this->name = $this->firstName . ($this->lastName ? " " . $this->lastName : "");
        $this->userID = @$con['user_id'];
    }

    /**
     * Check number valid
     * بررسی اعتبار شماره یا کد کشور
     *
     * @param string $country
     * @return boolean
     */
    public function isValid($country = '98'){
        return (bool)preg_match('/^(00|\+|)' . $country . '/', $this->num);
    }
}

class Location extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;
    function __construct($loc, $base){
        $this->_base = $base;
        $this->longitude = $loc['longitude'];
        $this->latitude = $loc['latitude'];
    }
}

class Entity extends MmbBase{
    /**
     * @var MMB
     */
    private $_base;
    function __construct($e, $base){
        $this->_base = $base;
        $this->type = $e['type'];
        $this->offset = $e['offset'];
        $this->len = $e['length'];
        if($this->type == "text_link")
            $this->url = @$e['url'];
        if($this->type == "text_mention")
            $this->user = new user(@$e['user'], $base);
        $this->lang = @$e['language'];
    }
}

class TelFile extends ICMsgData{
    /**
     * File id
     * آیدی فایل
     *
     * @var string
     */
    public $id;
    /**
     * File path
     * آدرس فایل
     * 
     * You can download file by following url:
     * https://api.telegram.org/file/bot[TOKEN]/[FILE_PATH]
     * شما با لینک بالا می توانید فایل را دانلود کنید
     * 
     * And you can use download function:
     * $myFile->download("temps/test.txt");
     * همچنین از تابع دانلود نیز می توانید استفاده کنید
     * 
     * @var string
     */
    public $path;
    /**
     * File size
     * حجم فایل
     *
     * @var int
     */
    public $size;
    /**
     * File unique id
     * آیدی یکتای فایل
     *
     * @var int
     */
    public $uniqueID;
    /**
     * @var MMB
     */
    private $_base;
    function __construct($f, $base){
        $this->_base = $base;
        $this->id = $f['file_id'];
        $this->path = $f['file_path'];
        $this->size = $f['file_size'];
        $this->uniqueID = $f['unique_id'];
    }
    
    /**
     * Download file
     * دانلود فایل
     *
     * @param string $path Paste path | محل قرار گیری فایل
     * @return bool
     */
    function download($path){
        return $this->_base->copyByFilePath($this->path, $path);
    }
    
}
<?php

/* ===========================================================================
 * Copyright (c) 2018-2021 Zindex Software
 *
 * Licensed under the MIT License
 * =========================================================================== */

// + https://github.com/opis/closure


namespace Opis\Closure;

defined('T_NAME_QUALIFIED') || define('T_NAME_QUALIFIED', -4);
defined('T_NAME_FULLY_QUALIFIED') || define('T_NAME_FULLY_QUALIFIED', -5);
defined('T_FN') || define('T_FN', -6);

use Closure;
use ReflectionFunction;

class ReflectionClosure extends ReflectionFunction
{
    protected $code;
    protected $tokens;
    protected $hashedName;
    protected $useVariables;
    protected $isStaticClosure;
    protected $isScopeRequired;
    protected $isBindingRequired;
    protected $isShortClosure;

    protected static $files = array();
    protected static $classes = array();
    protected static $functions = array();
    protected static $constants = array();
    protected static $structures = array();


    /**
     * ReflectionClosure constructor.
     * @param Closure $closure
     * @param string|null $code This is ignored. Do not use it
     * @throws \ReflectionException
     */
    public function __construct(Closure $closure, $code = null)
    {
        parent::__construct($closure);
    }

    /**
     * @return bool
     */
    public function isStatic()
    {
        if ($this->isStaticClosure === null) {
            $this->isStaticClosure = strtolower(substr($this->getCode(), 0, 6)) === 'static';
        }

        return $this->isStaticClosure;
    }

    public function isShortClosure()
    {
        if ($this->isShortClosure === null) {
            $code = $this->getCode();
            if ($this->isStatic()) {
                $code = substr($code, 6);
            }
            $this->isShortClosure = strtolower(substr(trim($code), 0, 2)) === 'fn';
        }

        return $this->isShortClosure;
    }

    /**
     * @return string
     */
    public function getCode()
    {
        if($this->code !== null){
            return $this->code;
        }

        $fileName = $this->getFileName();
        $line = $this->getStartLine() - 1;

        $className = null;

        if (null !== $className = $this->getClosureScopeClass()) {
            $className = '\\' . trim($className->getName(), '\\');
        }

        $builtin_types = self::getBuiltinTypes();
        $class_keywords = ['self', 'static', 'parent'];

        $ns = $this->getNamespaceName();
        $nsf = $ns == '' ? '' : ($ns[0] == '\\' ? $ns : '\\' . $ns);

        $_file = var_export($fileName, true);
        $_dir = var_export(dirname($fileName), true);
        $_namespace = var_export($ns, true);
        $_class = var_export(trim($className, '\\'), true);
        $_function = $ns . ($ns == '' ? '' : '\\') . '{closure}';
        $_method = ($className == '' ? '' : trim($className, '\\') . '::') . $_function;
        $_function = var_export($_function, true);
        $_method = var_export($_method, true);
        $_trait = null;

        $tokens = $this->getTokens();
        $state = $lastState = 'start';
        $inside_structure = false;
        $isShortClosure = false;
        $inside_structure_mark = 0;
        $open = 0;
        $code = '';
        $id_start = $id_start_ci = $id_name = $context = '';
        $classes = $functions = $constants = null;
        $use = array();
        $lineAdd = 0;
        $isUsingScope = false;
        $isUsingThisObject = false;

        for($i = 0, $l = count($tokens); $i < $l; $i++) {
            $token = $tokens[$i];
            switch ($state) {
                case 'start':
                    if ($token[0] === T_FUNCTION || $token[0] === T_STATIC) {
                        $code .= $token[1];
                        $state = $token[0] === T_FUNCTION ? 'function' : 'static';
                    } elseif ($token[0] === T_FN) {
                        $isShortClosure = true;
                        $code .= $token[1];
                        $state = 'closure_args';
                    }
                    break;
                case 'static':
                    if ($token[0] === T_WHITESPACE || $token[0] === T_COMMENT || $token[0] === T_FUNCTION) {
                        $code .= $token[1];
                        if ($token[0] === T_FUNCTION) {
                            $state = 'function';
                        }
                    } elseif ($token[0] === T_FN) {
                        $isShortClosure = true;
                        $code .= $token[1];
                        $state = 'closure_args';
                    } else {
                        $code = '';
                        $state = 'start';
                    }
                    break;
                case 'function':
                    switch ($token[0]){
                        case T_STRING:
                            $code = '';
                            $state = 'named_function';
                            break;
                        case '(':
                            $code .= '(';
                            $state = 'closure_args';
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                    }
                    break;
                case 'named_function':
                    if($token[0] === T_FUNCTION || $token[0] === T_STATIC){
                        $code = $token[1];
                        $state = $token[0] === T_FUNCTION ? 'function' : 'static';
                    } elseif ($token[0] === T_FN) {
                        $isShortClosure = true;
                        $code .= $token[1];
                        $state = 'closure_args';
                    }
                    break;
                case 'closure_args':
                    switch ($token[0]){
                        case T_NAME_QUALIFIED:
                            list($id_start, $id_start_ci, $id_name) = $this->parseNameQualified($token[1]);
                            $context = 'args';
                            $state = 'id_name';
                            $lastState = 'closure_args';
                            break;
                        case T_NS_SEPARATOR:
                        case T_STRING:
                            $id_start = $token[1];
                            $id_start_ci = strtolower($id_start);
                            $id_name = '';
                            $context = 'args';
                            $state = 'id_name';
                            $lastState = 'closure_args';
                            break;
                        case T_USE:
                            $code .= $token[1];
                            $state = 'use';
                            break;
                        case T_DOUBLE_ARROW:
                            $code .= $token[1];
                            if ($isShortClosure) {
                                $state = 'closure';
                            }
                            break;
                        case ':':
                            $code .= ':';
                            $state = 'return';
                            break;
                        case '{':
                            $code .= '{';
                            $state = 'closure';
                            $open++;
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                    }
                    break;
                case 'use':
                    switch ($token[0]){
                        case T_VARIABLE:
                            $use[] = substr($token[1], 1);
                            $code .= $token[1];
                            break;
                        case '{':
                            $code .= '{';
                            $state = 'closure';
                            $open++;
                            break;
                        case ':':
                            $code .= ':';
                            $state = 'return';
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                            break;
                    }
                    break;
                case 'return':
                    switch ($token[0]){
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            $code .= $token[1];
                            break;
                        case T_NS_SEPARATOR:
                        case T_STRING:
                            $id_start = $token[1];
                            $id_start_ci = strtolower($id_start);
                            $id_name = '';
                            $context = 'return_type';
                            $state = 'id_name';
                            $lastState = 'return';
                            break 2;
                        case T_NAME_QUALIFIED:
                            list($id_start, $id_start_ci, $id_name) = $this->parseNameQualified($token[1]);
                            $context = 'return_type';
                            $state = 'id_name';
                            $lastState = 'return';
                            break 2;
                        case T_DOUBLE_ARROW:
                            $code .= $token[1];
                            if ($isShortClosure) {
                                $state = 'closure';
                            }
                            break;
                        case '{':
                            $code .= '{';
                            $state = 'closure';
                            $open++;
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                            break;
                    }
                    break;
                case 'closure':
                    switch ($token[0]){
                        case T_CURLY_OPEN:
                        case T_DOLLAR_OPEN_CURLY_BRACES:
                        case '{':
                            $code .= is_array($token) ? $token[1] : $token;
                            $open++;
                            break;
                        case '}':
                            $code .= '}';
                            if(--$open === 0 && !$isShortClosure){
                                break 3;
                            } elseif ($inside_structure) {
                                $inside_structure = !($open === $inside_structure_mark);
                            }
                            break;
                        case '(':
                        case '[':
                            $code .= $token[0];
                            if ($isShortClosure) {
                                $open++;
                            }
                            break;
                        case ')':
                        case ']':
                            if ($isShortClosure) {
                                if ($open === 0) {
                                    break 3;
                                }
                                --$open;
                            }
                            $code .= $token[0];
                            break;
                        case ',':
                        case ';':
                            if ($isShortClosure && $open === 0) {
                                break 3;
                            }
                            $code .= $token[0];
                            break;
                        case T_LINE:
                            $code .= $token[2] - $line + $lineAdd;
                            break;
                        case T_FILE:
                            $code .= $_file;
                            break;
                        case T_DIR:
                            $code .= $_dir;
                            break;
                        case T_NS_C:
                            $code .= $_namespace;
                            break;
                        case T_CLASS_C:
                            $code .= $inside_structure ? $token[1] : $_class;
                            break;
                        case T_FUNC_C:
                            $code .= $inside_structure ? $token[1] : $_function;
                            break;
                        case T_METHOD_C:
                            $code .= $inside_structure ? $token[1] : $_method;
                            break;
                        case T_COMMENT:
                            if (substr($token[1], 0, 8) === '#trackme') {
                                $timestamp = time();
                                $code .= '/**' . PHP_EOL;
                                $code .= '* Date      : ' . date(DATE_W3C, $timestamp) . PHP_EOL;
                                $code .= '* Timestamp : ' . $timestamp . PHP_EOL;
                                $code .= '* Line      : ' . ($line + 1) . PHP_EOL;
                                $code .= '* File      : ' . $_file . PHP_EOL . '*/' . PHP_EOL;
                                $lineAdd += 5;
                            } else {
                                $code .= $token[1];
                            }
                            break;
                        case T_VARIABLE:
                            if($token[1] == '$this' && !$inside_structure){
                                $isUsingThisObject = true;
                            }
                            $code .= $token[1];
                            break;
                        case T_STATIC:
                        case T_NS_SEPARATOR:
                        case T_STRING:
                            $id_start = $token[1];
                            $id_start_ci = strtolower($id_start);
                            $id_name = '';
                            $context = 'root';
                            $state = 'id_name';
                            $lastState = 'closure';
                            break 2;
                        case T_NAME_QUALIFIED:
                            list($id_start, $id_start_ci, $id_name) = $this->parseNameQualified($token[1]);
                            $context = 'root';
                            $state = 'id_name';
                            $lastState = 'closure';
                            break 2;
                        case T_NEW:
                            $code .= $token[1];
                            $context = 'new';
                            $state = 'id_start';
                            $lastState = 'closure';
                            break 2;
                        case T_USE:
                            $code .= $token[1];
                            $context = 'use';
                            $state = 'id_start';
                            $lastState = 'closure';
                            break;
                        case T_INSTANCEOF:
                        case T_INSTEADOF:
                            $code .= $token[1];
                            $context = 'instanceof';
                            $state = 'id_start';
                            $lastState = 'closure';
                            break;
                        case T_OBJECT_OPERATOR:
                        case T_DOUBLE_COLON:
                            $code .= $token[1];
                            $lastState = 'closure';
                            $state = 'ignore_next';
                            break;
                        case T_FUNCTION:
                            $code .= $token[1];
                            $state = 'closure_args';
                            if (!$inside_structure) {
                                $inside_structure = true;
                                $inside_structure_mark = $open;
                            }
                            break;
                        case T_TRAIT_C:
                            if ($_trait === null) {
                                $startLine = $this->getStartLine();
                                $endLine = $this->getEndLine();
                                $structures = $this->getStructures();

                                $_trait = '';

                                foreach ($structures as &$struct) {
                                    if ($struct['type'] === 'trait' &&
                                        $struct['start'] <= $startLine &&
                                        $struct['end'] >= $endLine
                                    ) {
                                        $_trait = ($ns == '' ? '' : $ns . '\\') . $struct['name'];
                                        break;
                                    }
                                }

                                $_trait = var_export($_trait, true);
                            }

                            $code .= $_trait;
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                    }
                    break;
                case 'ignore_next':
                    switch ($token[0]){
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            $code .= $token[1];
                            break;
                        case T_CLASS:
                        case T_NEW:
                        case T_STATIC:
                        case T_VARIABLE:
                        case T_STRING:
                        case T_CLASS_C:
                        case T_FILE:
                        case T_DIR:
                        case T_METHOD_C:
                        case T_FUNC_C:
                        case T_FUNCTION:
                        case T_INSTANCEOF:
                        case T_LINE:
                        case T_NS_C:
                        case T_TRAIT_C:
                        case T_USE:
                            $code .= $token[1];
                            $state = $lastState;
                            break;
                        default:
                            $state = $lastState;
                            $i--;
                    }
                    break;
                case 'id_start':
                    switch ($token[0]){
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            $code .= $token[1];
                            break;
                        case T_NS_SEPARATOR:
                        case T_NAME_FULLY_QUALIFIED:
                        case T_STRING:
                        case T_STATIC:
                            $id_start = $token[1];
                            $id_start_ci = strtolower($id_start);
                            $id_name = '';
                            $state = 'id_name';
                            break 2;
                        case T_NAME_QUALIFIED:
                            list($id_start, $id_start_ci, $id_name) = $this->parseNameQualified($token[1]);
                            $state = 'id_name';
                            break 2;
                        case T_VARIABLE:
                            $code .= $token[1];
                            $state = $lastState;
                            break;
                        case T_CLASS:
                            $code .= $token[1];
                            $state = 'anonymous';
                            break;
                        default:
                            $i--;//reprocess last
                            $state = 'id_name';
                    }
                    break;
                case 'id_name':
                    switch ($token[0]){
                        case T_NAME_QUALIFIED:
                        case T_NS_SEPARATOR:
                        case T_STRING:
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            $id_name .= $token[1];
                            break;
                        case '(':
                            if ($isShortClosure) {
                                $open++;
                            }
                            if($context === 'new' || false !== strpos($id_name, '\\')){
                                if($id_start_ci === 'self' || $id_start_ci === 'static') {
                                    if (!$inside_structure) {
                                        $isUsingScope = true;
                                    }
                                } elseif ($id_start !== '\\' && !in_array($id_start_ci, $class_keywords)) {
                                    if ($classes === null) {
                                        $classes = $this->getClasses();
                                    }
                                    if (isset($classes[$id_start_ci])) {
                                        $id_start = $classes[$id_start_ci];
                                    }
                                    if($id_start[0] !== '\\'){
                                        $id_start = $nsf . '\\' . $id_start;
                                    }
                                }
                            } else {
                                if($id_start !== '\\'){
                                    if($functions === null){
                                        $functions = $this->getFunctions();
                                    }
                                    if(isset($functions[$id_start_ci])){
                                        $id_start = $functions[$id_start_ci];
                                    } elseif ($nsf !== '\\' && function_exists($nsf . '\\' . $id_start)) {
                                        $id_start = $nsf . '\\' . $id_start;
                                        // Cache it to functions array
                                        $functions[$id_start_ci] = $id_start;
                                    }
                                }
                            }
                            $code .= $id_start . $id_name . '(';
                            $state = $lastState;
                            break;
                        case T_VARIABLE:
                        case T_DOUBLE_COLON:
                            if($id_start !== '\\') {
                                if($id_start_ci === 'self' || $id_start_ci === 'parent'){
                                    if (!$inside_structure) {
                                        $isUsingScope = true;
                                    }
                                } elseif ($id_start_ci === 'static') {
                                    if (!$inside_structure) {
                                        $isUsingScope = $token[0] === T_DOUBLE_COLON;
                                    }
                                } elseif (!(\PHP_MAJOR_VERSION >= 7 && in_array($id_start_ci, $builtin_types))){
                                    if ($classes === null) {
                                        $classes = $this->getClasses();
                                    }
                                    if (isset($classes[$id_start_ci])) {
                                        $id_start = $classes[$id_start_ci];
                                    }
                                    if($id_start[0] !== '\\'){
                                        $id_start = $nsf . '\\' . $id_start;
                                    }
                                }
                            }

                            $code .= $id_start . $id_name . $token[1];
                            $state = $token[0] === T_DOUBLE_COLON ? 'ignore_next' : $lastState;
                            break;
                        default:
                            if($id_start !== '\\' && !defined($id_start)){
                                if($constants === null){
                                    $constants = $this->getConstants();
                                }
                                if(isset($constants[$id_start])){
                                    $id_start = $constants[$id_start];
                                } elseif($context === 'new'){
                                    if(in_array($id_start_ci, $class_keywords)) {
                                        if (!$inside_structure) {
                                            $isUsingScope = true;
                                        }
                                    } else {
                                        if ($classes === null) {
                                            $classes = $this->getClasses();
                                        }
                                        if (isset($classes[$id_start_ci])) {
                                            $id_start = $classes[$id_start_ci];
                                        }
                                        if ($id_start[0] !== '\\') {
                                            $id_start = $nsf . '\\' . $id_start;
                                        }
                                    }
                                } elseif($context === 'use' ||
                                    $context === 'instanceof' ||
                                    $context === 'args' ||
                                    $context === 'return_type' ||
                                    $context === 'extends' ||
                                    $context === 'root'
                                ){
                                    if(in_array($id_start_ci, $class_keywords)){
                                        if (!$inside_structure && !$id_start_ci === 'static') {
                                            $isUsingScope = true;
                                        }
                                    } elseif (!(\PHP_MAJOR_VERSION >= 7 && in_array($id_start_ci, $builtin_types))){
                                        if($classes === null){
                                            $classes = $this->getClasses();
                                        }
                                        if(isset($classes[$id_start_ci])){
                                            $id_start = $classes[$id_start_ci];
                                        }
                                        if($id_start[0] !== '\\'){
                                            $id_start = $nsf . '\\' . $id_start;
                                        }
                                    }
                                }
                            }
                            $code .= $id_start . $id_name;
                            $state = $lastState;
                            $i--;//reprocess last token
                    }
                    break;
                case 'anonymous':
                    switch ($token[0]) {
                        case T_NS_SEPARATOR:
                        case T_STRING:
                            $id_start = $token[1];
                            $id_start_ci = strtolower($id_start);
                            $id_name = '';
                            $state = 'id_name';
                            $context = 'extends';
                            $lastState = 'anonymous';
                        break;
                        case '{':
                            $state = 'closure';
                            if (!$inside_structure) {
                                $inside_structure = true;
                                $inside_structure_mark = $open;
                            }
                            $i--;
                            break;
                        default:
                            $code .= is_array($token) ? $token[1] : $token;
                    }
                    break;
            }
        }

        if ($isShortClosure) {
            $this->useVariables = $this->getStaticVariables();
        } else {
            $this->useVariables = empty($use) ? $use : array_intersect_key($this->getStaticVariables(), array_flip($use));
        }

        $this->isShortClosure = $isShortClosure;
        $this->isBindingRequired = $isUsingThisObject;
        $this->isScopeRequired = $isUsingScope;
        $this->code = $code;

        return $this->code;
    }

    /**
     * @return array
     */
    private static function getBuiltinTypes()
    {
        // PHP 5
        if (\PHP_MAJOR_VERSION === 5) {
            return ['array', 'callable'];
        }

        // PHP 8
        if (\PHP_MAJOR_VERSION === 8) {
            return ['array', 'callable', 'string', 'int', 'bool', 'float', 'iterable', 'void', 'object', 'mixed', 'false', 'null'];
        }

        // PHP 7
        switch (\PHP_MINOR_VERSION) {
            case 0:
                return ['array', 'callable', 'string', 'int', 'bool', 'float'];
            case 1:
                return ['array', 'callable', 'string', 'int', 'bool', 'float', 'iterable', 'void'];
            default:
                return ['array', 'callable', 'string', 'int', 'bool', 'float', 'iterable', 'void', 'object'];
        }
    }

    /**
     * @return array
     */
    public function getUseVariables()
    {
        if($this->useVariables !== null){
            return $this->useVariables;
        }

        $tokens = $this->getTokens();
        $use = array();
        $state = 'start';

        foreach ($tokens as &$token) {
            $is_array = is_array($token);

            switch ($state) {
                case 'start':
                    if ($is_array && $token[0] === T_USE) {
                        $state = 'use';
                    }
                    break;
                case 'use':
                    if ($is_array) {
                        if ($token[0] === T_VARIABLE) {
                            $use[] = substr($token[1], 1);
                        }
                    } elseif ($token == ')') {
                        break 2;
                    }
                    break;
            }
        }

        $this->useVariables = empty($use) ? $use : array_intersect_key($this->getStaticVariables(), array_flip($use));

        return $this->useVariables;
    }

    /**
     * return bool
     */
    public function isBindingRequired()
    {
        if($this->isBindingRequired === null){
            $this->getCode();
        }

        return $this->isBindingRequired;
    }

    /**
     * return bool
     */
    public function isScopeRequired()
    {
        if($this->isScopeRequired === null){
            $this->getCode();
        }

        return $this->isScopeRequired;
    }

    /**
     * @return string
     */
    protected function getHashedFileName()
    {
        if ($this->hashedName === null) {
            $this->hashedName = sha1($this->getFileName());
        }

        return $this->hashedName;
    }

    /**
     * @return array
     */
    protected function getFileTokens()
    {
        $key = $this->getHashedFileName();

        if (!isset(static::$files[$key])) {
            static::$files[$key] = token_get_all(file_get_contents($this->getFileName()));
        }

        return static::$files[$key];
    }

    /**
     * @return array
     */
    protected function getTokens()
    {
        if ($this->tokens === null) {
            $tokens = $this->getFileTokens();
            $startLine = $this->getStartLine();
            $endLine = $this->getEndLine();
            $results = array();
            $start = false;

            foreach ($tokens as &$token) {
                if (!is_array($token)) {
                    if ($start) {
                        $results[] = $token;
                    }

                    continue;
                }

                $line = $token[2];

                if ($line <= $endLine) {
                    if ($line >= $startLine) {
                        $start = true;
                        $results[] = $token;
                    }

                    continue;
                }

                break;
            }

            $this->tokens = $results;
        }

        return $this->tokens;
    }

    /**
     * @return array
     */
    protected function getClasses()
    {
        $key = $this->getHashedFileName();

        if (!isset(static::$classes[$key])) {
            $this->fetchItems();
        }

        return static::$classes[$key];
    }

    /**
     * @return array
     */
    protected function getFunctions()
    {
        $key = $this->getHashedFileName();

        if (!isset(static::$functions[$key])) {
            $this->fetchItems();
        }

        return static::$functions[$key];
    }

    /**
     * @return array
     */
    protected function getConstants()
    {
        $key = $this->getHashedFileName();

        if (!isset(static::$constants[$key])) {
            $this->fetchItems();
        }

        return static::$constants[$key];
    }

    /**
     * @return array
     */
    protected function getStructures()
    {
        $key = $this->getHashedFileName();

        if (!isset(static::$structures[$key])) {
            $this->fetchItems();
        }

        return static::$structures[$key];
    }

    protected function fetchItems()
    {
        $key = $this->getHashedFileName();

        $classes = array();
        $functions = array();
        $constants = array();
        $structures = array();
        $tokens = $this->getFileTokens();

        $open = 0;
        $state = 'start';
        $lastState = '';
        $prefix = '';
        $name = '';
        $alias = '';
        $isFunc = $isConst = false;

        $startLine = $endLine = 0;
        $structType = $structName = '';
        $structIgnore = false;

        foreach ($tokens as $token) {

            switch ($state) {
                case 'start':
                    switch ($token[0]) {
                        case T_CLASS:
                        case T_INTERFACE:
                        case T_TRAIT:
                            $state = 'before_structure';
                            $startLine = $token[2];
                            $structType = $token[0] == T_CLASS
                                                    ? 'class'
                                                    : ($token[0] == T_INTERFACE ? 'interface' : 'trait');
                            break;
                        case T_USE:
                            $state = 'use';
                            $prefix = $name = $alias = '';
                            $isFunc = $isConst = false;
                            break;
                        case T_FUNCTION:
                            $state = 'structure';
                            $structIgnore = true;
                            break;
                        case T_NEW:
                            $state = 'new';
                            break;
                        case T_OBJECT_OPERATOR:
                        case T_DOUBLE_COLON:
                            $state = 'invoke';
                            break;
                    }
                    break;
                case 'use':
                    switch ($token[0]) {
                        case T_FUNCTION:
                            $isFunc = true;
                            break;
                        case T_CONST:
                            $isConst = true;
                            break;
                        case T_NS_SEPARATOR:
                            $name .= $token[1];
                            break;
                        case T_STRING:
                            $name .= $token[1];
                            $alias = $token[1];
                            break;
                        case T_NAME_QUALIFIED:
                            $name .= $token[1];
                            $pieces = explode('\\', $token[1]);
                            $alias = end($pieces);
                            break;
                        case T_AS:
                            $lastState = 'use';
                            $state = 'alias';
                            break;
                        case '{':
                            $prefix = $name;
                            $name = $alias = '';
                            $state = 'use-group';
                            break;
                        case ',':
                        case ';':
                            if ($name === '' || $name[0] !== '\\') {
                                $name = '\\' . $name;
                            }

                            if ($alias !== '') {
                                if ($isFunc) {
                                    $functions[strtolower($alias)] = $name;
                                } elseif ($isConst) {
                                    $constants[$alias] = $name;
                                } else {
                                    $classes[strtolower($alias)] = $name;
                                }
                            }
                            $name = $alias = '';
                            $state = $token === ';' ? 'start' : 'use';
                            break;
                    }
                    break;
                case 'use-group':
                    switch ($token[0]) {
                        case T_NS_SEPARATOR:
                            $name .= $token[1];
                            break;
                        case T_NAME_QUALIFIED:
                            $name .= $token[1];
                            $pieces = explode('\\', $token[1]);
                            $alias = end($pieces);
                            break;
                        case T_STRING:
                            $name .= $token[1];
                            $alias = $token[1];
                            break;
                        case T_AS:
                            $lastState = 'use-group';
                            $state = 'alias';
                            break;
                        case ',':
                        case '}':

                            if ($prefix === '' || $prefix[0] !== '\\') {
                                $prefix = '\\' . $prefix;
                            }

                            if ($alias !== '') {
                                if ($isFunc) {
                                    $functions[strtolower($alias)] = $prefix . $name;
                                } elseif ($isConst) {
                                    $constants[$alias] = $prefix . $name;
                                } else {
                                    $classes[strtolower($alias)] = $prefix . $name;
                                }
                            }
                            $name = $alias = '';
                            $state = $token === '}' ? 'use' : 'use-group';
                            break;
                    }
                    break;
                case 'alias':
                    if ($token[0] === T_STRING) {
                        $alias = $token[1];
                        $state = $lastState;
                    }
                    break;
                case 'new':
                    switch ($token[0]) {
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            break 2;
                        case T_CLASS:
                            $state = 'structure';
                            $structIgnore = true;
                            break;
                        default:
                            $state = 'start';
                    }
                    break;
                case 'invoke':
                    switch ($token[0]) {
                        case T_WHITESPACE:
                        case T_COMMENT:
                        case T_DOC_COMMENT:
                            break 2;
                        default:
                            $state = 'start';
                    }
                    break;
                case 'before_structure':
                    if ($token[0] == T_STRING) {
                        $structName = $token[1];
                        $state = 'structure';
                    }
                    break;
                case 'structure':
                    switch ($token[0]) {
                        case '{':
                        case T_CURLY_OPEN:
                        case T_DOLLAR_OPEN_CURLY_BRACES:
                            $open++;
                            break;
                        case '}':
                            if (--$open == 0) {
                                if(!$structIgnore){
                                    $structures[] = array(
                                        'type' => $structType,
                                        'name' => $structName,
                                        'start' => $startLine,
                                        'end' => $endLine,
                                    );
                                }
                                $structIgnore = false;
                                $state = 'start';
                            }
                            break;
                        default:
                            if (is_array($token)) {
                                $endLine = $token[2];
                            }
                    }
                    break;
            }
        }

        static::$classes[$key] = $classes;
        static::$functions[$key] = $functions;
        static::$constants[$key] = $constants;
        static::$structures[$key] = $structures;
    }

    private function parseNameQualified($token)
    {
        $pieces = explode('\\', $token);

        $id_start = array_shift($pieces);

        $id_start_ci = strtolower($id_start);

        $id_name = '\\' . implode('\\', $pieces);

        return [$id_start, $id_start_ci, $id_name];
    }
}

use Serializable;
use SplObjectStorage;
use ReflectionObject;

/**
 * Provides a wrapper for serialization of closures
 */
class SerializableClosure implements Serializable
{
    /**
     * @var Closure Wrapped closure
     *
     * @see \Opis\Closure\SerializableClosure::getClosure()
     */
    protected $closure;

    /**
     * @var ReflectionClosure A reflection instance for closure
     *
     * @see \Opis\Closure\SerializableClosure::getReflector()
     */
    protected $reflector;

    /**
     * @var mixed Used at deserialization to hold variables
     *
     * @see \Opis\Closure\SerializableClosure::unserialize()
     * @see \Opis\Closure\SerializableClosure::getReflector()
     */
    protected $code;

    /**
     * @var string Closure's ID
     */
    protected $reference;

    /**
     * @var string Closure scope
     */
    protected $scope;

    /**
     * @var ClosureContext Context of closure, used in serialization
     */
    protected static $context;

    /**
     * @var ISecurityProvider|null
     */
    protected static $securityProvider;

    /** Array recursive constant*/
    const ARRAY_RECURSIVE_KEY = '¯\_(ツ)_/¯';

    /**
     * Constructor
     *
     * @param   Closure $closure Closure you want to serialize
     */
    public function __construct(Closure $closure)
    {
        $this->closure = $closure;
        if (static::$context !== null) {
            $this->scope = static::$context->scope;
            $this->scope->toserialize++;
        }
    }

    /**
     * Get the Closure object
     *
     * @return  Closure The wrapped closure
     */
    public function getClosure()
    {
        return $this->closure;
    }

    /**
     * Get the reflector for closure
     *
     * @return  ReflectionClosure
     */
    public function getReflector()
    {
        if ($this->reflector === null) {
            $this->reflector = new ReflectionClosure($this->closure);
            $this->code = null;
        }

        return $this->reflector;
    }

    /**
     * Implementation of magic method __invoke()
     */
    public function __invoke()
    {
        return call_user_func_array($this->closure, func_get_args());
    }

    /**
     * Implementation of Serializable::serialize()
     *
     * @return  string  The serialized closure
     */
    public function serialize()
    {
        if ($this->scope === null) {
            $this->scope = new ClosureScope();
            $this->scope->toserialize++;
        }

        $this->scope->serializations++;

        $scope = $object = null;
        $reflector = $this->getReflector();

        if($reflector->isBindingRequired()){
            $object = $reflector->getClosureThis();
            static::wrapClosures($object, $this->scope);
            if($scope = $reflector->getClosureScopeClass()){
                $scope = $scope->name;
            }
        } else {
            if($scope = $reflector->getClosureScopeClass()){
                $scope = $scope->name;
            }
        }

        $this->reference = spl_object_hash($this->closure);

        $this->scope[$this->closure] = $this;

        $use = $this->transformUseVariables($reflector->getUseVariables());
        $code = $reflector->getCode();

        $this->mapByReference($use);

        $ret = \serialize(array(
            'use' => $use,
            'function' => $code,
            'scope' => $scope,
            'this' => $object,
            'self' => $this->reference,
        ));

        if (static::$securityProvider !== null) {
            $data = static::$securityProvider->sign($ret);
            $ret =  '@' . $data['hash'] . '.' . $data['closure'];
        }

        if (!--$this->scope->serializations && !--$this->scope->toserialize) {
            $this->scope = null;
        }

        return $ret;
    }

    /**
     * Transform the use variables before serialization.
     *
     * @param  array  $data The Closure's use variables
     * @return array
     */
    protected function transformUseVariables($data)
    {
        return $data;
    }

    /**
     * Implementation of Serializable::unserialize()
     *
     * @param   string $data Serialized data
     * @throws SecurityException
     */
    public function unserialize($data)
    {
        ClosureStream::register();

        if (static::$securityProvider !== null) {
            if ($data[0] !== '@') {
                throw new SecurityException("The serialized closure is not signed. ".
                    "Make sure you use a security provider for both serialization and unserialization.");
            }

            if ($data[1] !== '{') {
                $separator = strpos($data, '.');
                if ($separator === false) {
                    throw new SecurityException('Invalid signed closure');
                }
                $hash = substr($data, 1, $separator - 1);
                $closure = substr($data, $separator + 1);

                $data = ['hash' => $hash, 'closure' => $closure];

                unset($hash, $closure);
            } else {
                $data = json_decode(substr($data, 1), true);
            }

            if (!is_array($data) || !static::$securityProvider->verify($data)) {
                throw new SecurityException("Your serialized closure might have been modified and it's unsafe to be unserialized. " .
                    "Make sure you use the same security provider, with the same settings, " .
                    "both for serialization and unserialization.");
            }

            $data = $data['closure'];
        } elseif ($data[0] === '@') {
            if ($data[1] !== '{') {
                $separator = strpos($data, '.');
                if ($separator === false) {
                    throw new SecurityException('Invalid signed closure');
                }
                $hash = substr($data, 1, $separator - 1);
                $closure = substr($data, $separator + 1);

                $data = ['hash' => $hash, 'closure' => $closure];

                unset($hash, $closure);
            } else {
                $data = json_decode(substr($data, 1), true);
            }

            if (!is_array($data) || !isset($data['closure']) || !isset($data['hash'])) {
                throw new SecurityException('Invalid signed closure');
            }

            $data = $data['closure'];
        }

        $this->code = \unserialize($data);

        // unset data
        unset($data);

        $this->code['objects'] = array();

        if ($this->code['use']) {
            $this->scope = new ClosureScope();
            $this->code['use'] = $this->resolveUseVariables($this->code['use']);
            $this->mapPointers($this->code['use']);
            extract($this->code['use'], EXTR_OVERWRITE | EXTR_REFS);
            $this->scope = null;
        }

        $this->closure = include(ClosureStream::STREAM_PROTO . '://' . $this->code['function']);

        if($this->code['this'] === $this){
            $this->code['this'] = null;
        }

        $this->closure = $this->closure->bindTo($this->code['this'], $this->code['scope']);

        if(!empty($this->code['objects'])){
            foreach ($this->code['objects'] as $item){
                $item['property']->setValue($item['instance'], $item['object']->getClosure());
            }
        }

        $this->code = $this->code['function'];
    }

    /**
     * Resolve the use variables after unserialization.
     *
     * @param  array  $data The Closure's transformed use variables
     * @return array
     */
    protected function resolveUseVariables($data)
    {
        return $data;
    }

    /**
     * Wraps a closure and sets the serialization context (if any)
     *
     * @param   Closure $closure Closure to be wrapped
     *
     * @return  self    The wrapped closure
     */
    public static function from(Closure $closure)
    {
        if (static::$context === null) {
            $instance = new static($closure);
        } elseif (isset(static::$context->scope[$closure])) {
            $instance = static::$context->scope[$closure];
        } else {
            $instance = new static($closure);
            static::$context->scope[$closure] = $instance;
        }

        return $instance;
    }

    /**
     * Increments the context lock counter or creates a new context if none exist
     */
    public static function enterContext()
    {
        if (static::$context === null) {
            static::$context = new ClosureContext();
        }

        static::$context->locks++;
    }

    /**
     * Decrements the context lock counter and destroy the context when it reaches to 0
     */
    public static function exitContext()
    {
        if (static::$context !== null && !--static::$context->locks) {
            static::$context = null;
        }
    }

    /**
     * @param string $secret
     */
    public static function setSecretKey($secret)
    {
        if(static::$securityProvider === null){
            static::$securityProvider = new SecurityProvider($secret);
        }
    }

    /**
     * @param ISecurityProvider $securityProvider
     */
    public static function addSecurityProvider(ISecurityProvider $securityProvider)
    {
        static::$securityProvider = $securityProvider;
    }

    /**
     * Remove security provider
     */
    public static function removeSecurityProvider()
    {
        static::$securityProvider = null;
    }

    /**
     * @return null|ISecurityProvider
     */
    public static function getSecurityProvider()
    {
        return static::$securityProvider;
    }

    /**
     * Wrap closures
     *
     * @internal
     * @param $data
     * @param ClosureScope|SplObjectStorage|null $storage
     */
    public static function wrapClosures(&$data, SplObjectStorage $storage = null)
    {
        if($storage === null){
            $storage = static::$context->scope;
        }

        if($data instanceof Closure){
            $data = static::from($data);
        } elseif (is_array($data)){
            if(isset($data[self::ARRAY_RECURSIVE_KEY])){
                return;
            }
            $data[self::ARRAY_RECURSIVE_KEY] = true;
            foreach ($data as $key => &$value){
                if($key === self::ARRAY_RECURSIVE_KEY){
                    continue;
                }
                static::wrapClosures($value, $storage);
            }
            unset($value);
            unset($data[self::ARRAY_RECURSIVE_KEY]);
        } elseif($data instanceof \stdClass){
            if(isset($storage[$data])){
                $data = $storage[$data];
                return;
            }
            $data = $storage[$data] = clone($data);
            foreach ($data as &$value){
                static::wrapClosures($value, $storage);
            }
            unset($value);
        } elseif (is_object($data) && ! $data instanceof static){
            if(isset($storage[$data])){
                $data = $storage[$data];
                return;
            }
            $instance = $data;
            $reflection = new ReflectionObject($instance);
            if(!$reflection->isUserDefined()){
                $storage[$instance] = $data;
                return;
            }
            $storage[$instance] = $data = $reflection->newInstanceWithoutConstructor();

            do{
                if(!$reflection->isUserDefined()){
                    break;
                }
                foreach ($reflection->getProperties() as $property){
                    if($property->isStatic() || !$property->getDeclaringClass()->isUserDefined()){
                        continue;
                    }
                    $property->setAccessible(true);
                    if (PHP_VERSION >= 7.4 && !$property->isInitialized($instance)) {
                        continue;
                    }
                    $value = $property->getValue($instance);
                    if(is_array($value) || is_object($value)){
                        static::wrapClosures($value, $storage);
                    }
                    $property->setValue($data, $value);
                };
            } while($reflection = $reflection->getParentClass());
        }
    }

    /**
     * Unwrap closures
     *
     * @internal
     * @param $data
     * @param SplObjectStorage|null $storage
     */
    public static function unwrapClosures(&$data, SplObjectStorage $storage = null)
    {
        if($storage === null){
            $storage = static::$context->scope;
        }

        if($data instanceof static){
            $data = $data->getClosure();
        } elseif (is_array($data)){
            if(isset($data[self::ARRAY_RECURSIVE_KEY])){
                return;
            }
            $data[self::ARRAY_RECURSIVE_KEY] = true;
            foreach ($data as $key => &$value){
                if($key === self::ARRAY_RECURSIVE_KEY){
                    continue;
                }
                static::unwrapClosures($value, $storage);
            }
            unset($data[self::ARRAY_RECURSIVE_KEY]);
        }elseif ($data instanceof \stdClass){
            if(isset($storage[$data])){
                return;
            }
            $storage[$data] = true;
            foreach ($data as &$property){
                static::unwrapClosures($property, $storage);
            }
        } elseif (is_object($data) && !($data instanceof Closure)){
            if(isset($storage[$data])){
                return;
            }
            $storage[$data] = true;
            $reflection = new ReflectionObject($data);

            do{
                if(!$reflection->isUserDefined()){
                    break;
                }
                foreach ($reflection->getProperties() as $property){
                    if($property->isStatic() || !$property->getDeclaringClass()->isUserDefined()){
                        continue;
                    }
                    $property->setAccessible(true);
                    if (PHP_VERSION >= 7.4 && !$property->isInitialized($data)) {
                        continue;
                    }
                    $value = $property->getValue($data);
                    if(is_array($value) || is_object($value)){
                        static::unwrapClosures($value, $storage);
                        $property->setValue($data, $value);
                    }
                };
            } while($reflection = $reflection->getParentClass());
        }
    }

    /**
     * Creates a new closure from arbitrary code,
     * emulating create_function, but without using eval
     *
     * @param string$args
     * @param string $code
     * @return Closure
     */
    public static function createClosure($args, $code)
    {
        ClosureStream::register();
        return include(ClosureStream::STREAM_PROTO . '://function(' . $args. '){' . $code . '};');
    }

    /**
     * Internal method used to map closure pointers
     * @internal
     * @param $data
     */
    protected function mapPointers(&$data)
    {
        $scope = $this->scope;

        if ($data instanceof static) {
            $data = &$data->closure;
        } elseif (is_array($data)) {
            if(isset($data[self::ARRAY_RECURSIVE_KEY])){
                return;
            }
            $data[self::ARRAY_RECURSIVE_KEY] = true;
            foreach ($data as $key => &$value){
                if($key === self::ARRAY_RECURSIVE_KEY){
                    continue;
                } elseif ($value instanceof static) {
                    $data[$key] = &$value->closure;
                } elseif ($value instanceof SelfReference && $value->hash === $this->code['self']){
                    $data[$key] = &$this->closure;
                } else {
                    $this->mapPointers($value);
                }
            }
            unset($value);
            unset($data[self::ARRAY_RECURSIVE_KEY]);
        } elseif ($data instanceof \stdClass) {
            if(isset($scope[$data])){
                return;
            }
            $scope[$data] = true;
            foreach ($data as $key => &$value){
                if ($value instanceof SelfReference && $value->hash === $this->code['self']){
                    $data->{$key} = &$this->closure;
                } elseif(is_array($value) || is_object($value)) {
                    $this->mapPointers($value);
                }
            }
            unset($value);
        } elseif (is_object($data) && !($data instanceof Closure)){
            if(isset($scope[$data])){
                return;
            }
            $scope[$data] = true;
            $reflection = new ReflectionObject($data);
            do{
                if(!$reflection->isUserDefined()){
                    break;
                }
                foreach ($reflection->getProperties() as $property){
                    if($property->isStatic() || !$property->getDeclaringClass()->isUserDefined()){
                        continue;
                    }
                    $property->setAccessible(true);
                    if (PHP_VERSION >= 7.4 && !$property->isInitialized($data)) {
                        continue;
                    }
                    $item = $property->getValue($data);
                    if ($item instanceof SerializableClosure || ($item instanceof SelfReference && $item->hash === $this->code['self'])) {
                        $this->code['objects'][] = array(
                            'instance' => $data,
                            'property' => $property,
                            'object' => $item instanceof SelfReference ? $this : $item,
                        );
                    } elseif (is_array($item) || is_object($item)) {
                        $this->mapPointers($item);
                        $property->setValue($data, $item);
                    }
                }
            } while($reflection = $reflection->getParentClass());
        }
    }

    /**
     * Internal method used to map closures by reference
     *
     * @internal
     * @param   mixed &$data
     */
    protected function mapByReference(&$data)
    {
        if ($data instanceof Closure) {
            if($data === $this->closure){
                $data = new SelfReference($this->reference);
                return;
            }

            if (isset($this->scope[$data])) {
                $data = $this->scope[$data];
                return;
            }

            $instance = new static($data);

            if (static::$context !== null) {
                static::$context->scope->toserialize--;
            } else {
                $instance->scope = $this->scope;
            }

            $data = $this->scope[$data] = $instance;
        } elseif (is_array($data)) {
            if(isset($data[self::ARRAY_RECURSIVE_KEY])){
                return;
            }
            $data[self::ARRAY_RECURSIVE_KEY] = true;
            foreach ($data as $key => &$value){
                if($key === self::ARRAY_RECURSIVE_KEY){
                    continue;
                }
                $this->mapByReference($value);
            }
            unset($value);
            unset($data[self::ARRAY_RECURSIVE_KEY]);
        } elseif ($data instanceof \stdClass) {
            if(isset($this->scope[$data])){
                $data = $this->scope[$data];
                return;
            }
            $instance = $data;
            $this->scope[$instance] = $data = clone($data);

            foreach ($data as &$value){
                $this->mapByReference($value);
            }
            unset($value);
        } elseif (is_object($data) && !$data instanceof SerializableClosure){
            if(isset($this->scope[$data])){
                $data = $this->scope[$data];
                return;
            }

            $instance = $data;
            $reflection = new ReflectionObject($data);
            if(!$reflection->isUserDefined()){
                $this->scope[$instance] = $data;
                return;
            }
            $this->scope[$instance] = $data = $reflection->newInstanceWithoutConstructor();

            do{
                if(!$reflection->isUserDefined()){
                    break;
                }
                foreach ($reflection->getProperties() as $property){
                    if($property->isStatic() || !$property->getDeclaringClass()->isUserDefined()){
                        continue;
                    }
                    $property->setAccessible(true);
                    if (PHP_VERSION >= 7.4 && !$property->isInitialized($instance)) {
                        continue;
                    }
                    $value = $property->getValue($instance);
                    if(is_array($value) || is_object($value)){
                        $this->mapByReference($value);
                    }
                    $property->setValue($data, $value);
                }
            } while($reflection = $reflection->getParentClass());
        }
    }

}

class SelfReference
{
    /**
     * @var string An unique hash representing the object
     */
    public $hash;

    /**
     * Constructor
     *
     * @param string $hash
     */
    public function __construct($hash)
    {
        $this->hash = $hash;
    }
}

class SecurityProvider implements ISecurityProvider
{
    /** @var  string */
    protected $secret;

    /**
     * SecurityProvider constructor.
     * @param string $secret
     */
    public function __construct($secret)
    {
        $this->secret = $secret;
    }

    /**
     * @inheritdoc
     */
    public function sign($closure)
    {
        return array(
            'closure' => $closure,
            'hash' => base64_encode(hash_hmac('sha256', $closure, $this->secret, true)),
        );
    }

    /**
     * @inheritdoc
     */
    public function verify(array $data)
    {
        return base64_encode(hash_hmac('sha256', $data['closure'], $this->secret, true)) === $data['hash'];
    }
}

use Exception;

/**
 * Security exception class
 */
class SecurityException extends Exception
{

}

interface ISecurityProvider
{
    /**
     * Sign serialized closure
     * @param string $closure
     * @return array
     */
    public function sign($closure);

    /**
     * Verify signature
     * @param array $data
     * @return bool
     */
    public function verify(array $data);
}


class ClosureStream
{
    const STREAM_PROTO = 'closure';

    protected static $isRegistered = false;

    protected $content;

    protected $length;

    protected $pointer = 0;

    function stream_open($path, $mode, $options, &$opened_path)
    {
        $this->content = "<?php\nreturn " . substr($path, strlen(static::STREAM_PROTO . '://')) . ";";
        $this->length = strlen($this->content);
        return true;
    }

    public function stream_read($count)
    {
        $value = substr($this->content, $this->pointer, $count);
        $this->pointer += $count;
        return $value;
    }

    public function stream_eof()
    {
        return $this->pointer >= $this->length;
    }

    public function stream_set_option($option, $arg1, $arg2)
    {
        return false;
    }

    public function stream_stat()
    {
        $stat = stat(__FILE__);
        $stat[7] = $stat['size'] = $this->length;
        return $stat;
    }

    public function url_stat($path, $flags)
    {
        $stat = stat(__FILE__);
        $stat[7] = $stat['size'] = $this->length;
        return $stat;
    }

    public function stream_seek($offset, $whence = SEEK_SET)
    {
        $crt = $this->pointer;

        switch ($whence) {
            case SEEK_SET:
                $this->pointer = $offset;
                break;
            case SEEK_CUR:
                $this->pointer += $offset;
                break;
            case SEEK_END:
                $this->pointer = $this->length + $offset;
                break;
        }

        if ($this->pointer < 0 || $this->pointer >= $this->length) {
            $this->pointer = $crt;
            return false;
        }

        return true;
    }

    public function stream_tell()
    {
        return $this->pointer;
    }

    public static function register()
    {
        if (!static::$isRegistered) {
            static::$isRegistered = stream_wrapper_register(static::STREAM_PROTO, __CLASS__);
        }
    }

}

/**
 * Closure scope class
 * @internal
 */
class ClosureScope extends \SplObjectStorage
{
    /**
     * @var integer Number of serializations in current scope
     */
    public $serializations = 0;

    /**
     * @var integer Number of closures that have to be serialized
     */
    public $toserialize = 0;
}

/**
 * Closure context class
 * @internal
 */
class ClosureContext
{
    /**
     * @var ClosureScope Closures scope
     */
    public $scope;

    /**
     * @var integer
     */
    public $locks;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->scope = new ClosureScope();
        $this->locks = 0;
    }
}
<?php

// Copyright (C): t.me/MMBlib

class Plugin extends MmbBase{

    /**
     * از این تابع برای شروع کار خود استفاده کنید
     *
     * @return void
     */
    public static function start() {
    }


    /**
     * گرفتن تنظیمات ذخیره شده این پلاگین
     *
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    protected static function getSetting($name, $default = null){
        return PluginSettings::get(static::class . '.' . $name, $default);
    }

    /**
     * ذخیره تنظیمات ذخیره شده این پلاگین
     *
     * @param string $name
     * @param mixed $value
     * @return mixed
     */
    protected static function setSetting($name, $value){
        return PluginSettings::set(static::class . '.' . $name, $value);
    }

}

final class Plugins{

    private static $classes = [];
    /**
     * پلاگینی را معرفی می کنید تا در زمان شروع اتم این کلاس ها نیز آغاز شوند
     *
     * @param string $class_name
     * @return void
     */
    public static function addPlugin($class_name){
        if(!in_array($class_name, self::$classes))
            self::$classes[] = $class_name;
    }

    public static function startAll(){
        foreach(self::$classes as $cl){
            $cl::start();
        }
    }

}
<?php

// Copyright (C): t.me/MMBlib

class Poll extends MmbBase{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var MMB
     */
    private $_base;

    /**
     * ID
     * آیدی
     *
     * @var string
     */
    public $id;
    /**
     * Question
     * سوال
     *
     * @var string
     */
    public $text;
    /**
     * Options
     * گزینه ها
     *
     * @var PollOpt[]
     */
    public $options;
    /**
     * Voters count
     * تعداد رای دهندگان
     *
     * @var int
     */
    public $votersCount;
    /**
     * Is poll closed
     * آیا پول بسته است
     *
     * @var bool
     */
    public $isClosed;
    /**
     * Is anonymous
     * آیا ناشناس است
     *
     * @var bool
     */
    public $isAnonymous;
    /**
     * Type
     * نوع
     *
     * @var string
     */
    public $type;
    public const TYPE_REGULAR = 'regular';
    public const TYPE_QUIZ = 'quiz';

    /**
     * Is allowed multiple
     * آیا چند گزینه ای فعال است
     *
     * @var bool
     */
    public $multiple;
    /**
     * Correct option id
     * ایندکس گزینه صحیح
     *
     * @var int
     */
    public $correct;
    /**
     * Explansion
     * توضیحات
     *
     * @var string
     */
    public $explan;
    /**
     * Explansion entities
     * موجودیت های توضیحات
     *
     * @var  Entity[]
     */
    public $explanEntites;
    /**
     * Amount of time in seconds the poll will be active after creation
     * زمان فعال بودن نظرسنجی
     *
     * @var int
     */
    public $openPreiod;
    /**
     * Point in time when the poll will be automatically closed
     * زمانی که نظرسنجی خودکار بسته می شود
     *
     * @var int
     */
    public $closeDate;

    function __construct($b, $base){
        $this->_base = $base;
        $this->id = $b['id'];
        $this->text = $b['question'];
        $this->options = [];
        $this->votersCount = $b['total_voter_count'];
        $_ = $b['options'];
        foreach($_ as $__){
            $this->options[] = new PollOpt($__, $this, $base);
        }
        $this->isClosed = $b['is_closed'];
        $this->isAnonymous = $b['is_anonymous'];
        $this->type = $b['type'];
        $this->multiple = $b['allows_multiple_answers'];
        $this->correct = $b['correct_option_id'];
        $this->explan = $b['explanation'];
        $this->explanation_entities = [];
        $_ = @$b['explanation_entities'];
        if($_)
        foreach($_ as $__){
            $this->explanation_entities[] = new Entity($__, $base);
        }
        $this->openPreiod = @$b['open_period'];
        $this->closeDate = @$b['close_date'];
    }
}

class PollOpt extends MmbBase{
    /**
     * @var Mmb
     */
    private $_base;
    /**
     * @var Poll
     */
    private $_basep;

    /**
     * Text
     * متن
     *
     * @var string
     */
    public $text;
    /**
     * Voters count
     * تعداد رای ها به این گزینه
     *
     * @var int
     */
    public $votersCount;
    /**
     * Cent
     * درصد رای این گزینه
     *
     * @var float
     */
    public $cent;
    public function __construct($a, Poll $poll, $base)
    {
        $this->_base = $base;
        $this->_basep = $poll;
        $this->text = $a['text'];
        $this->votersCount = $a['voter_count'];
        if($poll->votersCount == 0)
            $this->cent = 0;
        else
            $this->cent = $this->votersCount / $poll->votersCount * 100;
    }
}

class PollAnswer extends MmbBase implements IUser{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var Mmb
     */
    private $_base;

    /**
     * Poll id
     * شناسه نظرسنجی
     *
     * @var string
     */
    public $id;
    /**
     * User
     * کاربر رای دهنده
     *
     * @var User
     */
    public $user;
    /**
     * Chosen options
     * گزینه های انتخاب شده
     *
     * @var int[]
     */
    public $options;
    /**
     * Chosen count
     * تعداد انتخاب ها
     *
     * @var int
     */
    public $chosenCount;

    public function __construct($a, $base)
    {
        $this->_base = $base;
        $this->id = $a['poll_id'];
        $this->user = new User($a['user'], $base);
        $this->options = $a['option_ids'];
        $this->chosenCount = count($this->options);
    }

    public function __getUserID()
    {
        return $this->user->id;
    }
}
<?php

// Copyright (C): t.me/MMBlib


/**
 * کلاسی با ارث بری از این کلاس بسازید تا دیتا های عمومی خود را راحت در دست بگیرید!
 */
class Storage {

    private static $path = [];
    private static $datas = [];

    /**
     * تنظیم مسیر ذخیره دیتای این کلاس
     *
     * @param string $storagePath
     * @return void
     */
    public static function setPath($storagePath) {
        self::$path[static::class] = $storagePath;
    }

    /**
     * گرفتن مسیر ذخیره دیتای این کلاس
     *
     * @return string
     */
    public static function getPath() {
        return self::$path[static::class] ?? Atom::$storage;
    }

    public static function jsonFlag() {
        return JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE;
    }

    /**
     * دیتا را با سلکتور شما انتخاب می کند و بر میگرداند (تنها یک مقدار)
     * 
     * * Selector:
     * * `"menu.name.*.text|caption" = $data['menu']['name'][همه][text و caption]`
     *
     * @param string $selector
     * @param mixed $default
     * @return mixed
     */
    public static function get($selector, $default = null){
        $data = static::getBase();
        return ATool::selectorGet($data, $selector, $default);
    }

    /**
     * دیتا را با سلکتور شما انتخاب می کند و بررسی می کند که وجود دارد یا نه
     * 
     * * Selector:
     * * `"menu.name.*.text|caption" = $data['menu']['name'][همه][text و caption]`
     *
     * @param string $selector
     * @return mixed
     */
    public static function exists($selector){
        $data = static::getBase();
        return ATool::selectorExists($data, $selector);
    }

    /**
     * دیتا را با سلکتور شما انتخاب می کند و تمام انتخاب ها را بر میگرداند
     * 
     * * Selector:
     * * `"menu.name.*.text|caption" = $data['menu']['name'][همه][text و caption]`
     *
     * @param string $selector
     * @return array
     */
    public static function getList($selector){
        $data = static::getBase();
        return ATool::selectorGetList($data, $selector);
    }

    private static function _dataSet($data) {
        self::$datas[static::class] = $data;
    }

    /**
     * عین مقدار ذخیره شده را بر میگرداند
     *
     * @return array|null
     */
    public static function getBase(bool $load_require = false){
        if(
            !$load_require &&
            isset(self::$datas[static::class]) &&
            !Atom::runIsLong()
        ) {
            return self::$datas[static::class];
        }

        $target = static::getPath() . '/' . strtolower(static::class) . '.json';
        $file = file_exists($target) ? Files::get($target) : false;
        if($file){
            $data = @json_decode($file, true);
            self::$datas[static::class] = $data;
            return $data;
        }
        else{
            return [];
        }
    }

    /**
     * انتخاب، اجرای کالبک، ذخیره
     * 
     * با این تابع می توانید دیتا های مورد نظر را بگیرید و ویرایش کنید
     *
     * * Callback: function(&$data)
     * * `Globals::edit(function(&$data) { $data['test'] = strtolower($data['test']); });`
     * 
     * @param Callable|Closure|string|array $callback
     * @return void
     */
    public static function editBase($callback){
        $storage = static::getPath();
        if(!is_dir($storage)) mkdir($storage);
        if(!file_exists($storage . '/.htaccess')){
            file_put_contents($storage . '/403.php', '<?php echo "Access danied"; ?>');
            file_put_contents($storage . '/.htaccess', "<IfModule mod_rewrite.c>\nRewriteEngine On\n\nRewriteRule ^ 403.php\n</IfModule>");
        }

        $target = $storage . '/' . strtolower(static::class) . '.json';
        Files::editText($target, function($file) use(&$callback) {
            if($file){
                $data = @json_decode($file, true);
            }
            else{
                $data = [];
            }
            
            $callback($data);
            
            static::_dataSet($data);
            return json_encode($data, static::jsonFlag());
        });
    }

    /**
     * دیتا را با سلکتور شما انتخاب می کند و انتخاب ها را تنظیم می کند
     * 
     * * Selector:
     * * `"menu.name.*.text|caption" = $data['menu']['name'][همه][text و caption]`
     * 
     * @param string $selector
     * @param mixed $value
     * @return void
     */
    public static function set($selector, $value){
        static::editBase(function(&$data) use(&$selector, &$value) {

            ATool::selectorSet($data, $selector, $value);

        });
    }

    /**
     * انتخاب با سلکتور، اجرای کالبک، ذخیره
     * 
     * با این تابع می توانید دیتا های مورد نظر را بگیرید و ویرایش کنید
     * 
     * * توجه: تمام مقدار هایی که سلکتور انتخاب می کند، یک به یک به کالبک ارسال می شوند
     *
     * * Callback: `function(&$data)`
     * * `Globals::edit2('messages.*.text', function(&$text) { $text = strtoupper($text); });`
     * 
     * @param Callable|Closure|string|array $callback
     * @return void
     */
    public static function edit($selector, $callback) {
        static::editBase(function(&$data) use(&$selector, &$callback) {
            
            $sel = ATool::selectorGetSelectors($data, $selector);
            if($sel) {
                for($i = 0; $i < count($sel); $i++) {
                    $callback($sel[$i]);
                }
            }

        });
    }

    /**
     * عین مقداری که تعیین می کنید ذخیره می شود برای کل اطلاعات این کلاس
     *
     * @param array $data
     * @return void
     */
    public static function setBase(array $data){
        $storage = static::getPath();
        if(!is_dir($storage)) mkdir($storage);
        if(!file_exists($storage . '/.htaccess')){
            file_put_contents($storage . '/403.php', '<?php echo "Access danied"; ?>');
            file_put_contents($storage . '/.htaccess', "<IfModule mod_rewrite.c>\nRewriteEngine On\n\nRewriteRule ^ 403.php\n</IfModule>");
        }
        $target = $storage . '/' . strtolower(static::class) . '.json';
        Files::put($target, json_encode($data, static::jsonFlag()));
    }

    /**
     * دیتا را با سلکتور شما انتخاب می کند و آنها را حذف می کند
     * 
     * * Selector:
     * * `"menu.name.*.text|caption" = $data['menu']['name'][همه][text و caption]`
     * 
     * @param string $selector
     * @return void
     */
    public static function unset($selector){
        static::editBase(function(&$data) use(&$selector) {
            
            ATool::selectorUnset($data, $selector);
            
        });
    }

    /**
     * بررسی می کند متن شما شامل کاراکتر های دستوری سلکتور نیست
     *
     * @param string $name
     * @return bool
     */
    public static function selectorValidName($selector_name) {
        return ATool::selectorValidName($selector_name);
    }

    /**
     * تمام مقدار های ذخیره شده را حذف می کند
     *
     * @return void
     */
    public static function reset(){
        static::setBase([]);
    }

}

/**
 * مقدار هایی که در این کلاس تنظیم می کنید، ذخیره می شوند
 */
class Globals extends Storage{
}

/**
 * مقدار هایی که در این کلاس تنظیم می کنید، به عنوان تنظیمات ذخیره می شوند
 */
class Settings extends Storage{
}

/**
 * مقدار هایی که در این کلاس تنظیم می کنید، به عنوان تنظیمات پلاگین ذخیره می شوند
 */
class PluginSettings extends Storage{
}

/**
 * مقدار هایی که در این کلاس تنظیم می کنید، به عنوان تنظیمات ذخیره می شوند
 */
class Admins extends Storage{

    /**
     * آرایه ای شامل مدیران اصلی که ذخیره نمی شوند و باید در فایل کانفیگ هر بار تعریف شوند
     *
     * * `Admins::$developers = [1234, 5678, 9900];`
     * 
     * @var (int|string)[]
     */
    public static $developers = [];

    /**
     * آرایه شامل کلید آیدی ادمین و دسترسی های ادمین که ذخیره نمی شوند و باید در فایل کانفیگ هر بار تعریف شوند
     *
     * * `Admins::$admins = [ 1234 => [ 'per1' => true, 'per2' => false ], 4567 => [ 'per1' => false, 'per2' => false ], 9900 => '*' ];`
     * 
     * @var array
     */
    public static $admins = [];

    /**
     * افزودن ادمبن
     *
     * @param int $id
     * @return AdminPer|false
     */
    public static function addAdmin($id, $pers = []){
        __initAdminPerClass();
        if(!self::selectorValidName($id)) return false;
        if($admin = self::getAdmin($id)) return $admin;

        $admin = new AdminPer($id, $pers);
        parent::set("admins.$id", $pers);
        return $admin;
    }

    /**
     * حذف ادمین
     *
     * @param int $id
     * @return bool
     */
    public static function rmvAdmin($id){
        if(!self::selectorValidName($id)) return false;
        self::unset("admins.$id");
        return true;
    }

    /**
     * گرفتن اطلاعات ادمین
     *
     * @param int $id
     * @return AdminPer|false
     */
    public static function getAdmin($id = null){
        __initAdminPerClass();
        if(count(func_get_args()) == 0) $id = User::$this ? User::$this->id : false;
        
        if(in_array($id, self::$developers)) return new AdminPer($id, '*', false);
        if(isset(self::$admins[$id])) return new AdminPer($id, self::$admins[$id], false);

        if(!self::selectorValidName($id)) return false;
        $admin = parent::get("admins.$id", false);
        if($admin !== false)
            return new AdminPer($id, $admin);
        else
            return false;
    }

    /**
     * گرفتن لیست ادمین ها
     *
     * @return string[]
     */
    public static function getAdmins() {
        return array_keys(self::get('admins'));
    }

    /**
     * گرفتن لیست ادمین ها بصورت کامل
     *
     * @return string[]
     */
    public static function getAdminsFull() {
        $list = self::$developers;
        array_push($list, ...array_keys(self::$admins), ...array_keys(self::get('admins')));
        return $list;
    }

    /**
     * تعداد ادمین ها
     *
     * @return int
     */
    public static function count() {
        return count(self::get('admins'));
    }

    /**
     * تعداد ادمین ها بصورت کامل
     *
     * @return int
     */
    public static function countFull() {
        return count(self::get('admins')) + count(self::$developers) + count(self::$admins);
    }

    /**
     * بررسی ادمین بودن
     *
     * @param int $id
     * @return bool
     */
    public static function isAdmin($id = null){
        if(count(func_get_args()) == 0) $id = User::$this ? User::$this->id : false;
        
        if(in_array($id, self::$developers)) return true;
        if(isset(self::$admins[$id])) return true;

        if(!self::selectorValidName($id)) return false;
        return parent::exists("admins.$id");
    }

    /**
     * تنظیم دسترسی ادمین
     *
     * @param int $id
     * @param string $per
     * @param boolean $value
     * @return bool
     */
    public static function setPer($id, $per, $value = true){
        if(in_array($id, self::$developers)) return false;
        if(isset(self::$admins[$id])) return false;
        if(!self::selectorValidName($id)) return false;

        parent::set("admins.$id.$per", $value);
        return true;
    }

    /**
     * تنظیم دسترسی های ادمین
     *
     * @param int $id
     * @param array $pers
     * @return bool
     */
    public static function setPers($id, $pers){
        if(in_array($id, self::$developers)) return false;
        if(isset(self::$admins[$id])) return false;
        if(!self::selectorValidName($id)) return false;

        parent::set("admins.$id", $pers);
        return true;
    }

    /**
     * تنظیم مقدار های جدید
     *
     * @param int $id
     * @param array $pers
     * @return bool
     */
    public static function update($id, $pers){
        if(in_array($id, self::$developers)) return false;
        if(isset(self::$admins[$id])) return false;
        if(!self::selectorValidName($id)) return false;
        
        $ok = false;
        parent::edit("admins.$id", function(&$admin) use(&$pers, &$ok) {
            $ok = true;
            foreach($pers as $per => $value) {
                $admin[$per] = $value;
            }
        });
        if(!$ok) {
            parent::set("admins.$id", $pers);
        }
        return true;
    }

}

function __initAdminPerClass() {
    if(!class_exists('AdminPer')) {
        class AdminPer extends AdminPerBase {
        
            public function init()
            {
            }
        
            private $all_true = false;
            public function initAs($as)
            {
                if($as == '*') {
                    $this->all_true = true;
                }
            }
        
            public function __get($var) {
                if($this->all_true) {
                    return true;
                }
            }
        
            public function getData()
            {
            }
        
        }
    }
}
<?php

// Copyright (C): t.me/MMBlib

abstract class MmbTool extends MmbBase{

}

class Keys extends MmbTool{
    
    /**
     * Create require contact [single] button
     * ساخت تک دکمه درخواست شماره
     *
     * @param string $text
     * @return array
     */
    public static function reqContact($text){
        return ['text' => $text, 'contact' => true];
    }

    /**
     * Create require location [single] button
     * ساخت تک دکمه درخواست موقعیت
     *
     * @param string $text
     * @return array
     */
    public static function reqLocation($text){
        return ['text' => $text, 'location' => true];
    }

    /**
     * Create require poll [single] button
     * ساخت تک دکمه درخواست ساخت نظرسنجی
     *
     * @param string $text
     * @param string $type
     * @return array
     */
    public static function reqPoll($text, $type = Poll::TYPE_REGULAR){
        return ['text' => $text, 'poll' => ['type' => $type]];
    }

    /**
     * Create remove key action
     * ساخت حالت حذف دکمه ها
     *
     * @return string
     */
    public static function removeKey(){
        return '{"remove_keyboard": true}';
    }

    /**
     * Create force reply action
     * ساخت حالت ریپلای اجباری
     *
     * @return string
     */
    public static function forceRep($placeholder = null, $selective = null){
        $ar = [
            'force_reply' => true
        ];
        if($placeholder)
            $ar['input_field_placeholder'] = $placeholder;
        if($selective !== null)
            $ar['selective'] = $selective;
        return json_encode($ar);
    }
}

class Args extends MmbTool{

    /**
     * Create sendDice method args
     * ساخت ورودی های تابع ارسال تاس
     *
     * @param mixed $chat
     * @param string $emoji
     * @param mixed $reply
     * @param array $key
     * @param bool $disNotif
     * @return array
     */
    public static function sendDice($chat, $emoji = null, $reply = null, $key = null, $disNotif = null){
        return [
            'chat_id' => $chat,
            'emoji' => $emoji,
            'reply' => $reply,
            'disNotif' => $disNotif,
            'key' => $key
        ];
    }

    /**
     * Create sendPoll method args
     * ساخت ورودی های تابع ارسال نظرسنجی
     *
     * @param mixed $chat
     * @param string $text
     * @param string[] $options
     * @param bool $isAnonymous
     * @param mixed $reply
     * @param array $key
     * @param bool $disNotif
     * @return array
     */
    public static function sendPoll($chat, $text, $options, $isAnonymous = null, $reply = null, $key = null, $disNotif = null){
        return [
            'chat_id' => $chat,
            'text' => $text,
            'type' => Poll::TYPE_REGULAR,
            'options' => $options,
            'isAnonymous' => $isAnonymous,
            'reply' => $reply,
            'key' => $key,
            'disNotif' => $disNotif,
        ];
    }

    /**
     * Create sendPoll method args (Quiz mode)
     * ساخت ورودی های تابع ارسال نظرسنجی (حالت امتحان)
     *
     * @param mixed $chat
     * @param string $text
     * @param string[] $options
     * @param int $correct
     * @param string $explan
     * @param string $explanMode
     * @param bool $isAnonymous
     * @param mixed $reply
     * @param array $key
     * @param bool $disNotif
     * @return array
     */
    public static function sendPollQuiz($chat, $text, $options, $correct, $explan = null, $explanMode = null, $isAnonymous = null, $reply = null, $key = null, $disNotif = null){
        return [
            'chat_id' => $chat,
            'text' => $text,
            'type' => Poll::TYPE_QUIZ,
            'options' => $options,
            'correct' => $correct,
            'explan' => $explan,
            'explanMode' => $explanMode,
            'isAnonymous' => $isAnonymous,
            'reply' => $reply,
            'key' => $key,
            'disNotif' => $disNotif,
        ];
    }

    /**
     * Create chat permission array
     * ساخت آرایه تنظیمات دسترسی گروه
     *
     * @param boolean $sendmsg
     * @param boolean $sendmedia
     * @param boolean $sendpoll
     * @param boolean $sendother
     * @param boolean $webpre
     * @param boolean $changeinfo
     * @param boolean $invite
     * @param boolean $pin
     * @return array
     */
    public static function chatPer(bool $sendmsg = true, bool $sendmedia = true, bool $sendpoll = true, bool $sendother = true, bool $webpre = true, bool $changeinfo = false, bool $invite = false, bool $pin = false){
        return [
            'sendmsg' => $sendmsg,
            'sendmedia' => $sendmedia,
            'sendpoll' => $sendpoll,
            'sendother' => $sendother,
            'webpre' => $webpre,
            'changeinfo' => $changeinfo,
            'invite' => $invite,
            'pin' => $pin,
        ];
    }

    /**
     * Create promote permission array
     * ساخت آرایه تنظیمات دسترسی های ادمین
     *
     * @param boolean $changeinfo
     * @param boolean $invite
     * @param boolean $pin
     * @param boolean $managechat
     * @param boolean $delete
     * @param boolean $managevoicechat
     * @param boolean $restrict
     * @param boolean $promote
     * @param boolean $post
     * @param boolean $editpost
     * @param boolean $anonymous
     * @return array
     */
    public static function promotePer(bool $changeinfo = true, bool $invite = true, bool $pin = true, bool $managechat = true, bool $delete = true, bool $managevoicechat = true, bool $restrict = true, bool $promote = true, bool $post = true, bool $editpost = true, bool $anonymous = false){
        return [
            'changeinfo' => $changeinfo,
            'invite' => $invite,
            'pin' => $pin,
            'managechat' => $managechat,
            'delete' => $delete,
            'managevoicechat' => $managevoicechat,
            'restrict' => $restrict,
            'promote' => $promote,
            'post' => $post,
            'editpost' => $editpost,
            'anonymous' => $anonymous,
        ];
    }

    /**
     * Create promote permission array, for channel promote
     * ساخت آرایه تنظیمات دسترسی های ادمین برای کانال
     *
     * @param boolean $changeinfo
     * @param boolean $post
     * @param boolean $editpost
     * @param boolean $delete
     * @param boolean $invite
     * @param boolean $managevoicechat
     * @param boolean $managechat
     * @param boolean $restrict
     * @param boolean $promote
     * @return array
     */
    public static function promoteChannelPer(bool $changeinfo = true, bool $post = true, bool $editpost = false, bool $delete = false, bool $invite = false, bool $managevoicechat = false, bool $managechat = false, bool $restrict = true, bool $promote = false){
        return [
            'changeinfo' => $changeinfo,
            'invite' => $invite,
            'managechat' => $managechat,
            'delete' => $delete,
            'managevoicechat' => $managevoicechat,
            'restrict' => $restrict,
            'promote' => $promote,
            'post' => $post,
            'editpost' => $editpost,
        ];
    }

    /**
     * Create promote permission array, for group promote
     * ساخت آرایه تنظیمات دسترسی های ادمین برای گروه
     *
     * @param boolean $changeinfo
     * @param boolean $invite
     * @param boolean $pin
     * @param boolean $managechat
     * @param boolean $delete
     * @param boolean $managevoicechat
     * @param boolean $restrict
     * @param boolean $promote
     * @param boolean $anonymous
     * @return array
     */
    public static function promotePerGroup(bool $changeinfo = true, bool $invite = true, bool $pin = true, bool $managechat = true, bool $delete = false, bool $managevoicechat = false, bool $restrict = false, bool $promote = false, bool $anonymous = false){
        return [
            'changeinfo' => $changeinfo,
            'invite' => $invite,
            'pin' => $pin,
            'managechat' => $managechat,
            'delete' => $delete,
            'managevoicechat' => $managevoicechat,
            'restrict' => $restrict,
            'promote' => $promote,
            'anonymous' => $anonymous,
        ];
    }

    /**
     * Get none permission array
     * گرفتن آزایه دسترسی خالی(بدون دسترسی)
     *
     * @return array
     */
    public static function nonePer(){
        return [
            'sendmsg' => false,
            'sendmedia' => false,
            'sendpoll' => false,
            'sendother' => false,
            'webpre' => false,
            'changeinfo' => false,
            'invite' => false,
            'pin' => false,
            'managechat' => false,
            'delete' => false,
            'managevoicechat' => false,
            'restrict' => false,
            'promote' => false,
            'post' => false,
            'editpost' => false,
        ];
    }

}

class Files extends MmbTool {

    /**
     * باز کردن، ویرایش و ذخیره فایل
     * 
     * این تابع درخواست ها را در صف قرار می دهد تا ویرایشات همزمان کنترل شوند
     *
     * @param string $file
     * @param Closure|Callable|string|array $callback `function($content) { return $newContent; }`
     * @return bool
     */
    public static function editText($file, $callback) {
        // Create if not exists
        if(!file_exists($file))
            touch($file);
        
        // Open
        $f = fopen($file, "r+");
        if(!$f) return false;
        
        self::lock($f);
        $content = stream_get_contents($f);

        // Run
        $content = $callback($content);

        // Close
        fseek($f, 0);
        ftruncate($f, strlen($content));
        fwrite($f, $content);
        self::unlock($f);
        fclose($f);
        return true;
    }

    /**
     * باز کردن، ویرایش و ذخیره فایل
     * 
     * این تابع درخواست ها را در صف قرار می دهد تا ویرایشات همزمان کنترل شوند
     *
     * @param string $file
     * @param Closure|Callable|string|array $callback `function($stream)`
     * @return bool
     */
    public static function editStream($file, $callback) {
        // Create if not exists
        if(!file_exists($file))
            touch($file);

        // Open
        $f = fopen($file, "r+");
        if(!$f) return false;
        
        self::lock($f);

        // Run
        $callback($f);

        // Close
        self::unlock($f);
        fclose($f);
        return true;
    }

    /**
     * خواندن فایل
     *
     * این تابع درخواست ها را در صف قرار می دهد تا از خوانده شدن اطمینان حاصل کند
     * 
     * @param string $file
     * @param integer $maxLen
     * @return string|false
     */
    public static function get($file, $maxLen = -1) {
        if(!file_exists($file))
            return false;
        
        $f = fopen($file, "r");
        if(!$f) return false;
        
        self::lock($f);
        if($maxLen == -1)
            $content = stream_get_contents($f);
        else
            $content = stream_get_contents($f, $maxLen);
        self::unlock($f);
        fclose($f);

        return $content;
    }

    /**
     * نوشتن در فایل
     *
     * این تابع درخواست ها را در صف قرار می دهد تا از نوشته شدن اطمینان حاصل کند
     * 
     * @param string $file
     * @param string $content
     * @return bool
     */
    public static function put($file, $content) {
        // Create if not exists
        if(!file_exists($file))
            touch($file);

        $f = fopen($file, "r+");
        if(!$f) return false;

        self::lock($f);
        ftruncate($f, strlen($content));
        fwrite($f, $content);
        self::unlock($f);
        fclose($f);
        return true;
    }

    /**
     * افزودن به فایل
     *
     * این تابع درخواست ها را در صف قرار می دهد تا از نوشته شدن اطمینان حاصل کند
     * 
     * @param string $file
     * @param string $content
     * @return bool
     */
    public static function append($file, $content) {
        $f = fopen($file, "r+");
        if(!$f) return false;
        
        self::lock($f);
        fseek($f, 0, SEEK_END);
        fwrite($f, $content);
        self::unlock($f);
        fclose($f);
        return true;
    }

    /**
     * قفل کردن فایل
     * 
     * این تابع تا باز شدن فایل صبر می کند
     * * حداکثر تلاش را -1 بگذارید تا بدون محدودیت شود
     *
     * @param resource $f
     * @param integer $maxTry
     * @return void
     */
    public static function lock($f, $maxTry = 10000) {
        if($maxTry == -1) {
            while(!flock($f, LOCK_EX|LOCK_NB));
        }
        else {
            $try = 0;
            while(!flock($f, LOCK_EX|LOCK_NB) && ++$try < $maxTry);
        }
    }

    /**
     * باز کردن فایل
     *
     * @param resource $f
     * @return void
     */
    public static function unlock($f) {
        flock($f, LOCK_UN);
    }

}




//* ///////////////////////////////      Array tools      \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ *\\


define('SELECTOR_SET', 1);
define('SELECTOR_GET', 2);
define('SELECTOR_GET_LIST', 3);
define('SELECTOR_UNSET', 4);
define('SELECTOR_GET_SELECTORS', 5);
define('SELECTOR_EXISTS', 6);

/**
 * این کلاس برای آرایه های منظم و غیر کلید دار می باشد
 */
class ATool extends MmbTool {

    /**
     * افزودن مقدار به آرایه
     *
     * @param array $array
     * @param int $index
     * @param mixed $item
     * @return void
     */
    public static function insert(array &$array, $index, $item) {
        array_splice($array, $index, 0, [$item]);
    }

    /**
     * افزودن چند مقدار به آرایه
     *
     * @param array $array
     * @param int $index
     * @param array $items
     * @return void
     */
    public static function insertMulti(array &$array, $index, array $items) {
        array_splice($array, $index, 0, $items);
    }

    /**
     * حذف مقداری از آرایه
     *
     * @param array $array
     * @param int $index
     * @return void
     */
    public static function remove(array &$array, $index) {
        array_splice($array, $index, 1, []);
    }

    /**
     * حذف تمامی مقدار هایی که مطابقت دارند
     *
     * @param array $array
     * @param mixed $needle
     * @param boolean $equals
     * @return void
     */
    public static function remove2(array &$array, $needle, $equals = false) {
        if($equals)
            $array = array_filter($array, function($val) use(&$needle) {
                return $val !== $needle;
            });
        else
            $array = array_filter($array, function($val) use(&$needle) {
                return $val != $needle;
            });
        $array = array_values($array);
    }

    /**
     * جابجا کردن مقداری از ایندکسی به ایندکس دیگر
     *
     * @param array $array
     * @param int $from_index
     * @param int $to_index
     * @return void
     */
    public static function move(array &$array, $from_index, $to_index) {
        $value = @$array[$from_index];
        ATool::remove($array, $from_index);
        if($from_index < $to_index) $to_index--;
        ATool::insert($array, $to_index, $value);
    }

    /**
     * افزودن مقداری به انتهای آرایه
     *
     * @param array $array
     * @param mixed $item
     * @return void
     */
    public static function add(array &$array, $item) {
        $array[] = $item;
    }

    /**
     * افزودن چند مقدار به انتهای آرایه
     *
     * @param array $array
     * @param array $items
     * @return void
     */
    public static function addMulti(array &$array, array $items) {
        array_push($array, ...$items);
    }

    public static function isFirst(array &$array, $index) {
        return $array && $index == 0;
    }

    public static function isLast(array &$array, $index) {
        return $array && count($array) - 1 == $index;
    }

    /**
     * پردازش آرایه و انجام دادن عملیات های ابزارها
     *
     * @param array $array
     * @param boolean $assoc
     * @return array
     */
    public static function parse(array $array, $assoc = false) {
        $r = [];
        if($assoc) {
            foreach($array as $key => $value) {
                if($value instanceof AToolBase) {
                    $value->parse($r, true);
                }
                else {
                    $r[$key] = $value;
                }
            }
        }
        else {
            foreach($array as $value) {
                if($value instanceof AToolBase) {
                    $value->parse($r, false);
                }
                else {
                    $r[] = $value;
                }
            }
        }
        return $r;
    }

    /**
     * انتخاب با سلکتور
     * 
     * * Selector:
     * * `off` = `$array[off]`
     * * `settings.off` = `$array[settings][off]`
     * * `settings.*` = `$array[settings][همه]`
     * * `pers.*.can` = `$array[pers][همه][can]`
     * * `settings.off|text|other` = `$array[settings][off] و $array[settings][text] و $array[settings][other]`
     * * `mylist.+` = `$array[mylist][]`
     * *
     * * Flag:
     * * `SELECTOR_SET` : انتخاب ها را برابر مقداری که در ورودی آخر کرده اید می کند
     * * `SELECTOR_GET` : انتخاب را بر میگرداند(تنها یک انتخاب)
     * * `SELECTOR_GET_LIST` : لیستی از انتخاب ها بر میگرداند
     * * `SELECTOR_UNSET` : انتخاب ها را از آرایه حذف می کند
     * * `SELECTOR_GET_SELECTORS` : انتخاب ها را بصورت متغیر های رفرنس بر میگرداند
     * * `SELECTOR_EXISTS` : بررسی می کند که انتخاب وجود دارد
     *
     * @see https://mmblib.ir/docs/ATool/selector
     * 
     * @param array $array
     * @param string $selector
     * @param int $flag
     * @param mixed $arg
     * @return mixed
     */
    public static function selector(&$array, $selector, $flag, $arg = null) {
        $sel = [&$array];

        // Explode '.'
        $queries = explode('.', $selector);
        $ignoreIsset = $flag == SELECTOR_SET;
        $queriesLast = count($queries) - 1;
        $isUnset = $flag == SELECTOR_UNSET;

        // Run queries
        foreach($queries as $query_n => $query) {
            $last = $queriesLast == $query_n;

            // Query * (Select all)
            if($query == '*') {
                foreach($sel as $i => $_) {
                    // Select all
                    if(is_array($sel[$i]))
                    foreach($sel[$i] as $key => $__) {
                        if($ignoreIsset || isset($sel[$i][$key])) {
                            if($last && $isUnset) {
                                unset($sel[$i][$key]);
                            }
                            else
                                $sel[] = &$sel[$i][$key];
                        }
                    }
                    // Delete this
                    unset($sel[$i]);
                }
            }

            // Add query
            elseif($query == '+')  {
                foreach($sel as $i => $_) {
                    // Select all
                    $temp = null;
                    $sel[$i][] = &$temp;
                    $sel[$i] = &$temp;
                    unset($temp);
                }
            }

            // Query | (Or selector)
            elseif(strpos($query, '|') !== false) {
                $or = explode('|', $query);
                foreach($sel as $i => $_) {
                    // Select
                    foreach($or as $key) {
                        if($ignoreIsset || isset($sel[$i][$key])) {
                            if($last && $isUnset) {
                                unset($sel[$i][$key]);
                            }
                            else
                                $sel[] = &$sel[$i][$key];
                        }
                    }
                    // Delete this
                    unset($sel[$i]);
                }
            }

            // Normal
            else {
                foreach($sel as $i => $_) {
                    // Select
                    if($ignoreIsset || isset($sel[$i][$query])) {
                        if($last && $isUnset) {
                            unset($sel[$i][$query]);
                        }
                        else
                            $sel[$i] = &$sel[$i][$query];
                    }
                    else {
                        unset($sel[$i]);
                    }
                }
            }
        }

        switch($flag) {
            // Set
            case SELECTOR_SET:
                foreach($sel as $i => $_) {
                    $sel[$i] = $arg;
                }
            break;

            // Get
            case SELECTOR_GET:
                foreach($sel as $i => $_) {
                    return $sel[$i];
                }
                return $arg;

            // Get list
            case SELECTOR_GET_LIST:
                $list = [];
                foreach($sel as $i => $_) {
                    $list[] = $sel[$i];
                }
                return $list;
            
            // Get selectors
            case SELECTOR_GET_SELECTORS:
                return array_values($sel);

            // Unset
            case SELECTOR_GET:
                foreach($sel as $i => $_) {
                    unset($sel[$i]);
                }
            break;

            // Unset
            case SELECTOR_EXISTS:
                return $sel ? true : false;
        }
    }

    /**
     * انتخاب با سلکتور و تنظیم مقدار های آن
     * 
     * * `selectorSet($array, 'admins.*.pers.send|delete', true);` = `$array[admins][همه][pers][send و delete] = true;`
     *
     * @param array $array
     * @param string $selector
     * @param mixed $value
     * @return void
     */
    public static function selectorSet(&$array, $selector, $value) {
        self::selector($array, $selector, SELECTOR_SET, $value);
    }

    /**
     * انتخاب با سلکتور و گرفتن مقدار(تنها یک مقدار)
     * 
     * * `selectorGet($array, 'admins.*.pers.send');` = `$array[admins][همه][pers][send];`
     *
     * @param array $array
     * @param string $selector
     * @param mixed $default
     * @return mixed
     */
    public static function selectorGet(&$array, $selector, $default = null) {
        return self::selector($array, $selector, SELECTOR_GET, $default);
    }

    /**
     * انتخاب با سلکتور و گرفتن تمام مقدار ها
     * 
     * * `selectorGetList($array, 'admins.*');` = `$array[admins][همه];`
     *
     * @param array $array
     * @param string $selector
     * @return array
     */
    public static function selectorGetList(&$array, $selector) {
        return self::selector($array, $selector, SELECTOR_GET_LIST);
    }

    /**
     * انتخاب با سلکتور و گرفتن تمام مقدار ها
     * 
     * * `selectorGetSelectors($array, 'admins.*');` = `&$array[admins][همه];`
     *
     * @param array $array
     * @param string $selector
     * @return array
     */
    public static function selectorGetSelectors(&$array, $selector) {
        return self::selector($array, $selector, SELECTOR_GET_SELECTORS);
    }

    /**
     * انتخاب با سلکتور و حذف تعریف های آنها
     * 
     * * `selectorUnset($array, 'admins.*');` = `$array[admins][همه];`
     *
     * @param array $array
     * @param string $selector
     * @return void
     */
    public static function selectorUnset(&$array, $selector) {
        self::selector($array, $selector, SELECTOR_UNSET);
    }

    /**
     * انتخاب با سلکتور و بررسی وجود
     * 
     * * `selectorUnset($array, 'admins.*');` = `$array[admins][همه];`
     *
     * @param array $array
     * @param string $selector
     * @return bool
     */
    public static function selectorExists(&$array, $selector) {
        return self::selector($array, $selector, SELECTOR_EXISTS);
    }

    /**
     * این تابع بررسی می کند که سلکتور شما، شامل هیچ کاراکتر دستورات سلکتنور نباشد
     *
     * @param string $selector_name
     * @return bool
     */
    public static function selectorValidName($selector_name) {
        foreach(['.', '*', '|'] as $char) {
            if(strpos($selector_name, $char) !== false)
                return false;
        }
        return true;
    }

}
/**
 * پردازش آرایه و انجام دادن عملیات های ابزارها
 * 
 * * `این تابع، تابع کمکی است! تابع اصلی: ATool::parse`
 *
 * @param array $array
 * @param boolean $assoc
 * @return array
 */
function aParse(array $array, $assoc = false) {
    return ATool::parse($array, $assoc);
}


/**
 * این کلاس، کلاس ابزاریست که در کلاس آتول می توانید استفاده کنید
 */
abstract class AToolBase {
    public abstract function parse(&$array, $assoc = false);
}

class AEach extends AToolBase {
    private $array;
    private $callback;
    /**
     * این ایزار برای افزودن مقدار ها به این قسمت آرایست
     *
     * * اگر کالبک خالی باشد، بصورت خام آرایه قرار می گیرد
     * * callback: `function ($value [, $key])`
     * * return value: `$value` or `[$key, $value]`
     * 
     * @param array $array
     * @param callable $callback
     */
    public function __construct($array, $callback = null)
    {
        $this->array = $array;
        $this->callback = $callback;
    }

    public function parse(&$array, $assoc = false)
    {
        $callback = $this->callback;
        if($callback) {
            if($assoc) {
                foreach($this->array as $a => $b) {
                    list($key, $val) = $callback($b, $a);
                    $array[$key] = $val;
                }
            }
            else {
                foreach($this->array as $a => $b) {
                    $array[] = $callback($b, $a);
                }
            }
        }
        else {
            if($assoc) {
                foreach($this->array as $key => $value)
                    $array[$key] = $value;
            }
            else {
                array_push($array, ...$this->array);
            }
        }
    }
}
/**
 * این ایزار برای افزودن مقدار ها به این قسمت آرایست
 *
 * * اگر کالبک خالی باشد، بصورت خام آرایه قرار می گیرد
 * * callback: `function ($value [, $key])`
 * * return value: `$value` or `[$key, $value]`
 * 
 * * `این تابع، تابع کمکی است! کلاس اصلی: AEach`
 * 
 * @param array $array
 * @param callable $callback
 * @return AEach
 */
function aEach($array, $callback = null)
{
    return new AEach($array, $callback);
}

class AIter extends AToolBase {
    private $value;
    /**
     * این ابزار برای افزودن یک جنراتور به آرایه ست
     * 
     * روش های تعریف:
     * * 1: `function() { yield 1; yield 2; ... }`
     * * 2: `[1, 2, ...]`
     *
     * @param array|Generator|Callable|Closure $function
     */
    public function __construct($value)
    {
        $this->value = $value;
    }

    public function parse(&$array, $assoc = false)
    {
        if(is_callable($this->value) || $this->value instanceof Closure) {
            $v = $this->value;
            $v = $v();
            if($v instanceof Generator) {
                if($assoc) {
                    foreach($v as $key => $value) {
                        $array[$key] = $value;
                    }
                }
                else {
                    foreach($v as $value) {
                        $array[] = $value;
                    }
                }
            }
        }
        else {
            if($assoc) {
                foreach($this->value as $key => $value) {
                    $array[$key] = $value;
                }
            }
            else {
                foreach($this->value as $value) {
                    $array[] = $value;
                }
            }
        }
    }
}
/**
 * این ابزار برای افزودن یک جنراتور به آرایه ست
 * 
 * روش های تعریف:
 * * 1: `function() { yield 1; yield 2; ... }`
 * * 2: `[1, 2, ...]`
 * 
 * * `این تابع، تابع کمکی است! کلاس اصلی: AIter`
 *
 * @param array|Generator|Callable|Closure $function
 * @return AIter
 */
function aIter($value)
{
    return new AIter($value);
}


class AIf extends AToolBase {
    private $condision;
    private $value;
    /**
     * با این ابزار می توانید یک مقدار را در صورت صحیح بودن شرط قرار دهید
     *
     * @param bool|mixed $condision
     * @param mixed $value
     */
    public function __construct($condision, $value)
    {
        $this->condision = $condision ? true : false;
        $this->value = $value;
    }

    public function parse(&$array, $assoc = false)
    {
        if($this->condision) {
            $array[] = $this->value;
        }
    }
}
/**
 * با این ابزار می توانید یک مقدار را در صورت صحیح بودن شرط قرار دهید
 * 
 * * `این تابع، تابع کمکی است! کلاس اصلی: AIf`
 *
 * @param bool|mixed $condision
 * @param mixed $value
 * @return AIf
 */
function aIf($condision, $value)
{
    return new AIf($condision, $value);
}

class ANone extends AToolBase {
    
    /**
     * این ایزار برای زمانیست که نمی خواهید در این ایندکس مقداری قرار بگیرد
     * 
     * * Example: `ATool::parse([0, 1, $num >= 2 ? 2 : new ANone]);`
     * 
     */
    public function __construct() { }

    public function parse(&$array, $assoc = false) { }
}
/**
 * این ایزار برای زمانیست که نمی خواهید در این ایندکس مقداری قرار بگیرد
 * 
 * * Example: `aParse([0, 1, $num >= 2 ? 2 : aNone()]);`
 * * `این تابع، تابع کمکی است! کلاس اصلی: ANone`
 * 
 * @return ANone
 */
function aNone() {
    return new ANone;
}


<?php

// Copyright (C): t.me/MMBlib

class Upd extends MmbBase{
    /**
     * @var $this
     */
    public static $this = null;

    /**
     * @var array
     */
    private $_real;
    /**
     * @var MMB
     */
    private $_base;
    /**
     * update id
     * آیدی عددی آپدیت
     *
     * @var int
     */
    public $id;
    /**
     * Message
     * پیام
     *
     * @var Msg|null
     */
    public $msg;
    /**
     * Edited message
     * پیام ادیت شده
     *
     * @var Msg|null
     */
    public $editedMsg;
    /**
     * Callback (Clicked on inline keyboard)
     * کالبک (کلیک بر روی دکمه شیشه ای)
     *
     * @var Callback|null
     */
    public $callback;
    /**
     * Inline query (Type @yourbot ...)
     * اینلاین کوئری (تایپ @ربات_شما ...)
     *
     * @var inline|null
     */
    public $inline;
    /**
     * Channel post
     * پست کانال
     *
     * @var Msg|null
     */
    public $post;
    /**
     * Edited channel post
     * پست ویرایش شده کانال
     *
     * @var Msg|null
     */
    public $editedPost;
    /**
     * Choosen inline
     * انتخاب نتیجه اینلاین توسط کاربر
     *
     * @var ChosenInline|null
     */
    public $chosenInline;
    /**
     * New poll status
     * وضعیت جدید نظرسنجی
     *
     * @var Poll|null
     */
    public $poll;
    /**
     * New answer
     * پاسخ جدید نظرسنجی - برای نظرسنجی های غیر ناشناس
     *
     * @var PollAnswer|null
     */
    public $pollAnswer;
    /**
     * New user status in private chats
     * وضعیت جدید کاربر در چت خصوصی - مانند توقف ربات
     *
     * @var ChatMemberUpd|null
     */
    public $myChatMember;
    /**
     * New user status in chats
     * وضعیت جدید کاربر در چت - مانند بن شدن
     *
     * @var ChatMemberUpd|null
     */
    public $chatMember;
    /**
     * New join request
     * درخواست جدید عضویت
     *
     * @var JoinReq|null
     */
    public $joinReq;
    
    function __construct($upd, $base){
        $this->_real = $upd;
        $this->_base = $base;
        $this->id = $upd['update_id'];
        if(isset($upd['message'])){
            $this->msg = new msg($upd['message'], $base);
        }elseif(isset($upd['edited_message'])){
            $this->editedMsg = new msg($upd['edited_message'], $base);
        }elseif(isset($upd['callback_query'])){
            $this->callback = new callback($upd['callback_query'], $base);
        }
        elseif(isset($upd['inline_query'])){
            $this->inline = new inline($upd['inline_query'], $base);
        }
        elseif(isset($upd['channel_post'])){
            $this->post = new msg($upd['channel_post'], $base);
        }
        elseif(isset($upd['edited_channel_post'])){
            $this->editedPost = new msg($upd['edited_channel_post'], $base);
        }
        elseif(isset($upd['chosen_inline_result'])){
            $this->chosenInline = new ChosenInline($upd['chosen_inline_result'], $base);
        }
        elseif(isset($upd['poll'])){
            $this->poll = new Poll($upd['poll'], $base);
        }
        elseif(isset($upd['poll_answer'])){
            $this->pollAnswer = new PollAnswer($upd['poll_answer'], $base);
        }
        elseif(isset($upd['my_chat_member'])){
            $this->myChatMember = new ChatMemberUpd($upd['my_chat_member'], $base);
        }
        elseif(isset($upd['chat_member'])){
            $this->chatMember = new ChatMemberUpd($upd['chat_member'], $base);
        }
        elseif(isset($upd['chat_join_request'])){
            $this->joinReq = new JoinReq($upd['chat_join_request'], $base);
        }
        if(!self::$this){
            self::$this = $this;
        }
    }
    
    /**
     * Get real update
     * دریافت آپدیت دریافتی واقعی
     *
     * @return array
     */
    function real(){
        $real = $this->_real;
        settype($real, "array");
        return $real;
    }
}
<?php /* In the name of Allah = بسم اللّه الرّحمن الرّحیم */

/**
 * @فارسی : توابع زمان و تاریخ هجری شمسی (جلالی) در پی اچ پی
 * @name: Hijri_Shamsi,Solar(Jalali) Date and Time Functions
 * <AUTHOR> Reza Gholampanahi & WebSite : http://jdf.scr.ir
 * @License: GNU/LGPL _ Open Source & Free : [all functions]
 * @Version: 2.76 =>[ 1399/11/28 = 1442/07/04 = 2021/02/16 ]
 */

/*	F	*/
function jdate($format, $timestamp = '', $none = '', $time_zone = 'Asia/Tehran', $tr_num = 'fa') {

  $T_sec = 0;/* <= رفع خطاي زمان سرور ، با اعداد '+' و '-' بر حسب ثانيه */

  if ($time_zone != 'local') date_default_timezone_set(($time_zone === '') ? 'Asia/Tehran' : $time_zone);
  $ts = $T_sec + (($timestamp === '') ? time() : tr_num($timestamp));
  $date = explode('_', date('H_i_j_n_O_P_s_w_Y', $ts));
  list($j_y, $j_m, $j_d) = gregorian_to_jalali($date[8], $date[3], $date[2]);
  $doy = ($j_m < 7) ? (($j_m - 1) * 31) + $j_d - 1 : (($j_m - 7) * 30) + $j_d + 185;
  $kab = (((($j_y + 12) % 33) % 4) == 1) ? 1 : 0;
  $sl = strlen($format);
  $out = '';
  for ($i = 0; $i < $sl; $i++) {
    $sub = substr($format, $i, 1);
    if ($sub == '\\') {
      $out .= substr($format, ++$i, 1);
      continue;
    }
    switch ($sub) {

      case 'E':
      case 'R':
      case 'x':
      case 'X':
        $out .= 'http://jdf.scr.ir';
        break;

      case 'B':
      case 'e':
      case 'g':
      case 'G':
      case 'h':
      case 'I':
      case 'T':
      case 'u':
      case 'Z':
        $out .= date($sub, $ts);
        break;

      case 'a':
        $out .= ($date[0] < 12) ? 'ق.ظ' : 'ب.ظ';
        break;

      case 'A':
        $out .= ($date[0] < 12) ? 'قبل از ظهر' : 'بعد از ظهر';
        break;

      case 'b':
        $out .= (int) ($j_m / 3.1) + 1;
        break;

      case 'c':
        $out .= $j_y . '/' . $j_m . '/' . $j_d . ' ،' . $date[0] . ':' . $date[1] . ':' . $date[6] . ' ' . $date[5];
        break;

      case 'C':
        $out .= (int) (($j_y + 99) / 100);
        break;

      case 'd':
        $out .= ($j_d < 10) ? '0' . $j_d : $j_d;
        break;

      case 'D':
        $out .= jdate_words(array('kh' => $date[7]), ' ');
        break;

      case 'f':
        $out .= jdate_words(array('ff' => $j_m), ' ');
        break;

      case 'F':
        $out .= jdate_words(array('mm' => $j_m), ' ');
        break;

      case 'H':
        $out .= $date[0];
        break;

      case 'i':
        $out .= $date[1];
        break;

      case 'j':
        $out .= $j_d;
        break;

      case 'J':
        $out .= jdate_words(array('rr' => $j_d), ' ');
        break;

      case 'k';
        $out .= tr_num(100 - (int) ($doy / ($kab + 365.24) * 1000) / 10, $tr_num);
        break;

      case 'K':
        $out .= tr_num((int) ($doy / ($kab + 365.24) * 1000) / 10, $tr_num);
        break;

      case 'l':
        $out .= jdate_words(array('rh' => $date[7]), ' ');
        break;

      case 'L':
        $out .= $kab;
        break;

      case 'm':
        $out .= ($j_m > 9) ? $j_m : '0' . $j_m;
        break;

      case 'M':
        $out .= jdate_words(array('km' => $j_m), ' ');
        break;

      case 'n':
        $out .= $j_m;
        break;

      case 'N':
        $out .= $date[7] + 1;
        break;

      case 'o':
        $jdw = ($date[7] == 6) ? 0 : $date[7] + 1;
        $dny = 364 + $kab - $doy;
        $out .= ($jdw > ($doy + 3) and $doy < 3) ? $j_y - 1 : (((3 - $dny) > $jdw and $dny < 3) ? $j_y + 1 : $j_y);
        break;

      case 'O':
        $out .= $date[4];
        break;

      case 'p':
        $out .= jdate_words(array('mb' => $j_m), ' ');
        break;

      case 'P':
        $out .= $date[5];
        break;

      case 'q':
        $out .= jdate_words(array('sh' => $j_y), ' ');
        break;

      case 'Q':
        $out .= $kab + 364 - $doy;
        break;

      case 'r':
        $key = jdate_words(array('rh' => $date[7], 'mm' => $j_m));
        $out .= $date[0] . ':' . $date[1] . ':' . $date[6] . ' ' . $date[4] . ' ' . $key['rh'] . '، ' . $j_d . ' ' . $key['mm'] . ' ' . $j_y;
        break;

      case 's':
        $out .= $date[6];
        break;

      case 'S':
        $out .= 'ام';
        break;

      case 't':
        $out .= ($j_m != 12) ? (31 - (int) ($j_m / 6.5)) : ($kab + 29);
        break;

      case 'U':
        $out .= $ts;
        break;

      case 'v':
        $out .= jdate_words(array('ss' => ($j_y % 100)), ' ');
        break;

      case 'V':
        $out .= jdate_words(array('ss' => $j_y), ' ');
        break;

      case 'w':
        $out .= ($date[7] == 6) ? 0 : $date[7] + 1;
        break;

      case 'W':
        $avs = (($date[7] == 6) ? 0 : $date[7] + 1) - ($doy % 7);
        if ($avs < 0) $avs += 7;
        $num = (int) (($doy + $avs) / 7);
        if ($avs < 4) {
          $num++;
        } elseif ($num < 1) {
          $num = ($avs == 4 or $avs == ((((($j_y % 33) % 4) - 2) == ((int) (($j_y % 33) * 0.05))) ? 5 : 4)) ? 53 : 52;
        }
        $aks = $avs + $kab;
        if ($aks == 7) $aks = 0;
        $out .= (($kab + 363 - $doy) < $aks and $aks < 3) ? '01' : (($num < 10) ? '0' . $num : $num);
        break;

      case 'y':
        $out .= substr($j_y, 2, 2);
        break;

      case 'Y':
        $out .= $j_y;
        break;

      case 'z':
        $out .= $doy;
        break;

      default:
        $out .= $sub;
    }
  }
  return ($tr_num != 'en') ? tr_num($out, 'fa', '.') : $out;
}

/*	F	*/
function jstrftime($format, $timestamp = '', $none = '', $time_zone = 'Asia/Tehran', $tr_num = 'fa') {

  $T_sec = 0;/* <= رفع خطاي زمان سرور ، با اعداد '+' و '-' بر حسب ثانيه */

  if ($time_zone != 'local') date_default_timezone_set(($time_zone === '') ? 'Asia/Tehran' : $time_zone);
  $ts = $T_sec + (($timestamp === '') ? time() : tr_num($timestamp));
  $date = explode('_', date('h_H_i_j_n_s_w_Y', $ts));
  list($j_y, $j_m, $j_d) = gregorian_to_jalali($date[7], $date[4], $date[3]);
  $doy = ($j_m < 7) ? (($j_m - 1) * 31) + $j_d - 1 : (($j_m - 7) * 30) + $j_d + 185;
  $kab = (((($j_y + 12) % 33) % 4) == 1) ? 1 : 0;
  $sl = strlen($format);
  $out = '';
  for ($i = 0; $i < $sl; $i++) {
    $sub = substr($format, $i, 1);
    if ($sub == '%') {
      $sub = substr($format, ++$i, 1);
    } else {
      $out .= $sub;
      continue;
    }
    switch ($sub) {

        /* Day */
      case 'a':
        $out .= jdate_words(array('kh' => $date[6]), ' ');
        break;

      case 'A':
        $out .= jdate_words(array('rh' => $date[6]), ' ');
        break;

      case 'd':
        $out .= ($j_d < 10) ? '0' . $j_d : $j_d;
        break;

      case 'e':
        $out .= ($j_d < 10) ? ' ' . $j_d : $j_d;
        break;

      case 'j':
        $out .= str_pad($doy + 1, 3, 0, STR_PAD_LEFT);
        break;

      case 'u':
        $out .= $date[6] + 1;
        break;

      case 'w':
        $out .= ($date[6] == 6) ? 0 : $date[6] + 1;
        break;

        /* Week */
      case 'U':
        $avs = (($date[6] < 5) ? $date[6] + 2 : $date[6] - 5) - ($doy % 7);
        if ($avs < 0) $avs += 7;
        $num = (int) (($doy + $avs) / 7) + 1;
        if ($avs > 3 or $avs == 1) $num--;
        $out .= ($num < 10) ? '0' . $num : $num;
        break;

      case 'V':
        $avs = (($date[6] == 6) ? 0 : $date[6] + 1) - ($doy % 7);
        if ($avs < 0) $avs += 7;
        $num = (int) (($doy + $avs) / 7);
        if ($avs < 4) {
          $num++;
        } elseif ($num < 1) {
          $num = ($avs == 4 or $avs == ((((($j_y % 33) % 4) - 2) == ((int) (($j_y % 33) * 0.05))) ? 5 : 4)) ? 53 : 52;
        }
        $aks = $avs + $kab;
        if ($aks == 7) $aks = 0;
        $out .= (($kab + 363 - $doy) < $aks and $aks < 3) ? '01' : (($num < 10) ? '0' . $num : $num);
        break;

      case 'W':
        $avs = (($date[6] == 6) ? 0 : $date[6] + 1) - ($doy % 7);
        if ($avs < 0) $avs += 7;
        $num = (int) (($doy + $avs) / 7) + 1;
        if ($avs > 3) $num--;
        $out .= ($num < 10) ? '0' . $num : $num;
        break;

        /* Month */
      case 'b':
      case 'h':
        $out .= jdate_words(array('km' => $j_m), ' ');
        break;

      case 'B':
        $out .= jdate_words(array('mm' => $j_m), ' ');
        break;

      case 'm':
        $out .= ($j_m > 9) ? $j_m : '0' . $j_m;
        break;

        /* Year */
      case 'C':
        $tmp = (int) ($j_y / 100);
        $out .= ($tmp > 9) ? $tmp : '0' . $tmp;
        break;

      case 'g':
        $jdw = ($date[6] == 6) ? 0 : $date[6] + 1;
        $dny = 364 + $kab - $doy;
        $out .= substr(($jdw > ($doy + 3) and $doy < 3) ? $j_y - 1 : (((3 - $dny) > $jdw and $dny < 3) ? $j_y + 1 : $j_y), 2, 2);
        break;

      case 'G':
        $jdw = ($date[6] == 6) ? 0 : $date[6] + 1;
        $dny = 364 + $kab - $doy;
        $out .= ($jdw > ($doy + 3) and $doy < 3) ? $j_y - 1 : (((3 - $dny) > $jdw and $dny < 3) ? $j_y + 1 : $j_y);
        break;

      case 'y':
        $out .= substr($j_y, 2, 2);
        break;

      case 'Y':
        $out .= $j_y;
        break;

        /* Time */
      case 'H':
        $out .= $date[1];
        break;

      case 'I':
        $out .= $date[0];
        break;

      case 'l':
        $out .= ($date[0] > 9) ? $date[0] : ' ' . (int) $date[0];
        break;

      case 'M':
        $out .= $date[2];
        break;

      case 'p':
        $out .= ($date[1] < 12) ? 'قبل از ظهر' : 'بعد از ظهر';
        break;

      case 'P':
        $out .= ($date[1] < 12) ? 'ق.ظ' : 'ب.ظ';
        break;

      case 'r':
        $out .= $date[0] . ':' . $date[2] . ':' . $date[5] . ' ' . (($date[1] < 12) ? 'قبل از ظهر' : 'بعد از ظهر');
        break;

      case 'R':
        $out .= $date[1] . ':' . $date[2];
        break;

      case 'S':
        $out .= $date[5];
        break;

      case 'T':
        $out .= $date[1] . ':' . $date[2] . ':' . $date[5];
        break;

      case 'X':
        $out .= $date[0] . ':' . $date[2] . ':' . $date[5];
        break;

      case 'z':
        $out .= date('O', $ts);
        break;

      case 'Z':
        $out .= date('T', $ts);
        break;

        /* Time and Date Stamps */
      case 'c':
        $key = jdate_words(array('rh' => $date[6], 'mm' => $j_m));
        $out .= $date[1] . ':' . $date[2] . ':' . $date[5] . ' ' . date('P', $ts) . ' ' . $key['rh'] . '، ' . $j_d . ' ' . $key['mm'] . ' ' . $j_y;
        break;

      case 'D':
        $out .= substr($j_y, 2, 2) . '/' . (($j_m > 9) ? $j_m : '0' . $j_m) . '/' . (($j_d < 10) ? '0' . $j_d : $j_d);
        break;

      case 'F':
        $out .= $j_y . '-' . (($j_m > 9) ? $j_m : '0' . $j_m) . '-' . (($j_d < 10) ? '0' . $j_d : $j_d);
        break;

      case 's':
        $out .= $ts;
        break;

      case 'x':
        $out .= substr($j_y, 2, 2) . '/' . (($j_m > 9) ? $j_m : '0' . $j_m) . '/' . (($j_d < 10) ? '0' . $j_d : $j_d);
        break;

        /* Miscellaneous */
      case 'n':
        $out .= "\n";
        break;

      case 't':
        $out .= "\t";
        break;

      case '%':
        $out .= '%';
        break;

      default:
        $out .= $sub;
    }
  }
  return ($tr_num != 'en') ? tr_num($out, 'fa', '.') : $out;
}

/*	F	*/
function jmktime($h = '', $m = '', $s = '', $jm = '', $jd = '', $jy = '', $none = '', $timezone = 'Asia/Tehran') {
  if ($timezone != 'local') date_default_timezone_set($timezone);
  if ($h === '') {
    return time();
  } else {
    list($h, $m, $s, $jm, $jd, $jy) = explode('_', tr_num($h . '_' . $m . '_' . $s . '_' . $jm . '_' . $jd . '_' . $jy));
    if ($m === '') {
      return mktime($h);
    } else {
      if ($s === '') {
        return mktime($h, $m);
      } else {
        if ($jm === '') {
          return mktime($h, $m, $s);
        } else {
          $jdate = explode('_', jdate('Y_j', '', '', $timezone, 'en'));
          if ($jd === '') {
            list($gy, $gm, $gd) = jalali_to_gregorian($jdate[0], $jm, $jdate[1]);
            return mktime($h, $m, $s, $gm);
          } else {
            if ($jy === '') {
              list($gy, $gm, $gd) = jalali_to_gregorian($jdate[0], $jm, $jd);
              return mktime($h, $m, $s, $gm, $gd);
            } else {
              list($gy, $gm, $gd) = jalali_to_gregorian($jy, $jm, $jd);
              return mktime($h, $m, $s, $gm, $gd, $gy);
            }
          }
        }
      }
    }
  }
}

/*	F	*/
function jgetdate($timestamp = '', $none = '', $timezone = 'Asia/Tehran', $tn = 'en') {
  $ts = ($timestamp === '') ? time() : tr_num($timestamp);
  $jdate = explode('_', jdate('F_G_i_j_l_n_s_w_Y_z', $ts, '', $timezone, $tn));
  return array(
    'seconds' => tr_num((int) tr_num($jdate[6]), $tn),
    'minutes' => tr_num((int) tr_num($jdate[2]), $tn),
    'hours' => $jdate[1],
    'mday' => $jdate[3],
    'wday' => $jdate[7],
    'mon' => $jdate[5],
    'year' => $jdate[8],
    'yday' => $jdate[9],
    'weekday' => $jdate[4],
    'month' => $jdate[0],
    0 => tr_num($ts, $tn)
  );
}

/*	F	*/
function jcheckdate($jm, $jd, $jy) {
  list($jm, $jd, $jy) = explode('_', tr_num($jm . '_' . $jd . '_' . $jy));
  $l_d = ($jm == 12 and ((($jy + 12) % 33) % 4) != 1) ? 29 : (31 - (int) ($jm / 6.5));
  return ($jm > 12 or $jd > $l_d or $jm < 1 or $jd < 1 or $jy < 1) ? false : true;
}

/*	F	*/
function tr_num($str, $mod = 'en', $mf = '٫') {
  $num_a = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.');
  $key_a = array('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹', $mf);
  return ($mod == 'fa') ? str_replace($num_a, $key_a, $str) : str_replace($key_a, $num_a, $str);
}

/*	F	*/
function jdate_words($array, $mod = '') {
  foreach ($array as $type => $num) {
    $num = (int) tr_num($num);
    switch ($type) {

      case 'ss':
        $sl = strlen($num);
        $xy3 = substr($num, 2 - $sl, 1);
        $h3 = $h34 = $h4 = '';
        if ($xy3 == 1) {
          $p34 = '';
          $k34 = array('ده', 'یازده', 'دوازده', 'سیزده', 'چهارده', 'پانزده', 'شانزده', 'هفده', 'هجده', 'نوزده');
          $h34 = $k34[substr($num, 2 - $sl, 2) - 10];
        } else {
          $xy4 = substr($num, 3 - $sl, 1);
          $p34 = ($xy3 == 0 or $xy4 == 0) ? '' : ' و ';
          $k3 = array('', '', 'بیست', 'سی', 'چهل', 'پنجاه', 'شصت', 'هفتاد', 'هشتاد', 'نود');
          $h3 = $k3[$xy3];
          $k4 = array('', 'یک', 'دو', 'سه', 'چهار', 'پنج', 'شش', 'هفت', 'هشت', 'نه');
          $h4 = $k4[$xy4];
        }
        $array[$type] = (($num > 99) ? str_replace(
          array('12', '13', '14', '19', '20'),
          array('هزار و دویست', 'هزار و سیصد', 'هزار و چهارصد', 'هزار و نهصد', 'دوهزار'),
          substr($num, 0, 2)
        ) . ((substr($num, 2, 2) == '00') ? '' : ' و ') : '') . $h3 . $p34 . $h34 . $h4;
        break;

      case 'mm':
        $key = array('فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور', 'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند');
        $array[$type] = $key[$num - 1];
        break;

      case 'rr':
        $key = array(
          'یک', 'دو', 'سه', 'چهار', 'پنج', 'شش', 'هفت', 'هشت', 'نه', 'ده', 'یازده', 'دوازده', 'سیزده', 'چهارده', 'پانزده', 'شانزده', 'هفده', 'هجده', 'نوزده', 'بیست', 'بیست و یک', 'بیست و دو', 'بیست و سه', 'بیست و چهار', 'بیست و پنج', 'بیست و شش', 'بیست و هفت', 'بیست و هشت', 'بیست و نه', 'سی', 'سی و یک'
        );
        $array[$type] = $key[$num - 1];
        break;

      case 'rh':
        $key = array('یکشنبه', 'دوشنبه', 'سه شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه', 'شنبه');
        $array[$type] = $key[$num];
        break;

      case 'sh':
        $key = array('مار', 'اسب', 'گوسفند', 'میمون', 'مرغ', 'سگ', 'خوک', 'موش', 'گاو', 'پلنگ', 'خرگوش', 'نهنگ');
        $array[$type] = $key[$num % 12];
        break;

      case 'mb':
        $key = array('حمل', 'ثور', 'جوزا', 'سرطان', 'اسد', 'سنبله', 'میزان', 'عقرب', 'قوس', 'جدی', 'دلو', 'حوت');
        $array[$type] = $key[$num - 1];
        break;

      case 'ff':
        $key = array('بهار', 'تابستان', 'پاییز', 'زمستان');
        $array[$type] = $key[(int) ($num / 3.1)];
        break;

      case 'km':
        $key = array('فر', 'ار', 'خر', 'تی‍', 'مر', 'شه‍', 'مه‍', 'آب‍', 'آذ', 'دی', 'به‍', 'اس‍');
        $array[$type] = $key[$num - 1];
        break;

      case 'kh':
        $key = array('ی', 'د', 'س', 'چ', 'پ', 'ج', 'ش');
        $array[$type] = $key[$num];
        break;

      default:
        $array[$type] = $num;
    }
  }
  return ($mod === '') ? $array : implode($mod, $array);
}


/**  Gregorian & Jalali (Hijri_Shamsi,Solar) Date Converter Functions
Author: JDF.SCR.IR =>> Download Full Version :  http://jdf.scr.ir/jdf
License: GNU/LGPL _ Open Source & Free :: Version: 2.80 : [2020=1399]
---------------------------------------------------------------------
355746=361590-5844 & 361590=(30*33*365)+(30*8) & 5844=(16*365)+(16/4)
355666=355746-79-1 & 355668=355746-79+1 &  1595=605+990 &  605=621-16
990=30*33 & 12053=(365*33)+(32/4) & 36524=(365*100)+(100/4)-(100/100)
1461=(365*4)+(4/4) & 146097=(365*400)+(400/4)-(400/100)+(400/400)  */

/*	F	*/
function gregorian_to_jalali($gy, $gm, $gd, $mod = '') {
   list($gy, $gm, $gd) = explode('_', tr_num($gy . '_' . $gm . '_' . $gd));/* <= Extra :اين سطر ، جزء تابع اصلي نيست */
  $g_d_m = array(0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334);
  $gy2 = ($gm > 2) ? ($gy + 1) : $gy;
  $days = 355666 + (365 * $gy) + ((int) (($gy2 + 3) / 4)) - ((int) (($gy2 + 99) / 100)) + ((int) (($gy2 + 399) / 400)) + $gd + $g_d_m[$gm - 1];
  $jy = -1595 + (33 * ((int) ($days / 12053)));
  $days %= 12053;
  $jy += 4 * ((int) ($days / 1461));
  $days %= 1461;
  if ($days > 365) {
    $jy += (int) (($days - 1) / 365);
    $days = ($days - 1) % 365;
  }
  if ($days < 186) {
    $jm = 1 + (int) ($days / 31);
    $jd = 1 + ($days % 31);
  } else {
    $jm = 7 + (int) (($days - 186) / 30);
    $jd = 1 + (($days - 186) % 30);
  }
  return ($mod == '') ? array($jy, $jm, $jd) : $jy . $mod . $jm . $mod . $jd;
}

/*	F	*/
function jalali_to_gregorian($jy, $jm, $jd, $mod = '') {
   list($jy, $jm, $jd) = explode('_', tr_num($jy . '_' . $jm . '_' . $jd));/* <= Extra :اين سطر ، جزء تابع اصلي نيست */
  $jy += 1595;
  $days = -355668 + (365 * $jy) + (((int) ($jy / 33)) * 8) + ((int) ((($jy % 33) + 3) / 4)) + $jd + (($jm < 7) ? ($jm - 1) * 31 : (($jm - 7) * 30) + 186);
  $gy = 400 * ((int) ($days / 146097));
  $days %= 146097;
  if ($days > 36524) {
    $gy += 100 * ((int) (--$days / 36524));
    $days %= 36524;
    if ($days >= 365) $days++;
  }
  $gy += 4 * ((int) ($days / 1461));
  $days %= 1461;
  if ($days > 365) {
    $gy += (int) (($days - 1) / 365);
    $days = ($days - 1) % 365;
  }
  $gd = $days + 1;
  $sal_a = array(0, 31, (($gy % 4 == 0 and $gy % 100 != 0) or ($gy % 400 == 0)) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
  for ($gm = 0; $gm < 13 and $gd > $sal_a[$gm]; $gm++) $gd -= $sal_a[$gm];
  return ($mod == '') ? array($gy, $gm, $gd) : $gy . $mod . $gm . $mod . $gd;
}
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule ^ 403.php
</IfModule>